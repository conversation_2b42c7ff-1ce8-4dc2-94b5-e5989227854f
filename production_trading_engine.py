"""
Production-Ready Quantitative Trading Engine
Institutional-grade trading platform with advanced strategies and risk management
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

from output_manager import OutputManager

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

@dataclass
class Order:
    """Production order object"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: datetime = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    strategy_id: str = ""
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class Position:
    """Production position tracking"""
    symbol: str
    quantity: float
    avg_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime
    strategy_id: str = ""

class RiskManager:
    """Institutional-grade risk management system"""
    
    def __init__(self, max_portfolio_risk: float = 0.02, max_position_size: float = 0.05):
        self.max_portfolio_risk = max_portfolio_risk  # 2% max portfolio risk
        self.max_position_size = max_position_size    # 5% max position size
        self.var_confidence = 0.95
        self.stress_scenarios = self._initialize_stress_scenarios()
        
    def _initialize_stress_scenarios(self) -> Dict:
        """Initialize stress testing scenarios"""
        return {
            'market_crash': {'equity_shock': -0.20, 'vol_shock': 2.0},
            'flash_crash': {'equity_shock': -0.10, 'vol_shock': 3.0},
            'volatility_spike': {'equity_shock': 0.0, 'vol_shock': 2.5},
            'correlation_breakdown': {'correlation_shock': 0.8}
        }
    
    def calculate_var(self, returns: np.ndarray, confidence: float = 0.95) -> float:
        """Calculate Value at Risk"""
        if len(returns) < 30:
            return 0.0
        return np.percentile(returns, (1 - confidence) * 100)
    
    def calculate_expected_shortfall(self, returns: np.ndarray, confidence: float = 0.95) -> float:
        """Calculate Expected Shortfall (Conditional VaR)"""
        var = self.calculate_var(returns, confidence)
        return returns[returns <= var].mean()
    
    def stress_test_portfolio(self, positions: Dict[str, Position], scenario: str) -> Dict:
        """Perform stress testing on portfolio"""
        if scenario not in self.stress_scenarios:
            return {}
        
        scenario_params = self.stress_scenarios[scenario]
        stressed_pnl = 0.0
        
        for symbol, position in positions.items():
            if 'equity_shock' in scenario_params:
                shocked_price = position.avg_price * (1 + scenario_params['equity_shock'])
                position_pnl = position.quantity * (shocked_price - position.avg_price)
                stressed_pnl += position_pnl
        
        return {
            'scenario': scenario,
            'stressed_pnl': stressed_pnl,
            'stressed_return': stressed_pnl / sum(p.market_value for p in positions.values()) if positions else 0
        }
    
    def check_position_limits(self, symbol: str, quantity: float, portfolio_value: float, 
                            current_price: float) -> Tuple[bool, str]:
        """Check if position violates risk limits"""
        position_value = abs(quantity * current_price)
        position_weight = position_value / portfolio_value if portfolio_value > 0 else 0
        
        if position_weight > self.max_position_size:
            return False, f"Position size {position_weight:.2%} exceeds limit {self.max_position_size:.2%}"
        
        return True, "Position within limits"

class OrderManagementSystem:
    """Production Order Management System"""
    
    def __init__(self, risk_manager: RiskManager):
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.risk_manager = risk_manager
        self.order_counter = 0
        self.execution_latency_ms = 1.5  # Target sub-2ms execution
        
    def generate_order_id(self) -> str:
        """Generate unique order ID"""
        self.order_counter += 1
        return f"ORD_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.order_counter:06d}"
    
    async def submit_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                          quantity: float, price: Optional[float] = None,
                          strategy_id: str = "") -> Order:
        """Submit order with risk checks"""
        order_id = self.generate_order_id()
        
        # Risk checks
        portfolio_value = sum(p.market_value for p in self.positions.values())
        current_price = price or self._get_market_price(symbol)
        
        risk_ok, risk_msg = self.risk_manager.check_position_limits(
            symbol, quantity, portfolio_value, current_price
        )
        
        if not risk_ok:
            order = Order(order_id, symbol, side, order_type, quantity, price, 
                         status=OrderStatus.REJECTED, strategy_id=strategy_id)
            logging.warning(f"Order rejected: {risk_msg}")
            return order
        
        order = Order(order_id, symbol, side, order_type, quantity, price, 
                     strategy_id=strategy_id)
        self.orders[order_id] = order
        
        # Simulate order execution
        await self._execute_order(order)
        
        return order
    
    async def _execute_order(self, order: Order):
        """Simulate order execution with realistic latency"""
        await asyncio.sleep(self.execution_latency_ms / 1000)  # Convert to seconds
        
        # Simulate market execution
        fill_price = order.price or self._get_market_price(order.symbol)
        
        # Add realistic slippage
        slippage = np.random.normal(0, 0.0001)  # 1 basis point average slippage
        fill_price *= (1 + slippage)
        
        order.status = OrderStatus.FILLED
        order.filled_quantity = order.quantity
        order.avg_fill_price = fill_price
        
        # Update positions
        self._update_position(order)
        
        logging.info(f"Order {order.order_id} filled: {order.quantity} {order.symbol} @ {fill_price:.6f}")
    
    def _get_market_price(self, symbol: str) -> float:
        """Get current market price (placeholder for real market data)"""
        # In production, this would connect to real market data feeds
        base_prices = {
            'BONK': 0.000035,
            'WIF': 2.20,
            'POPCAT': 1.15,
            'FARTCOIN': 1.28,
            'SOL': 150.0
        }
        return base_prices.get(symbol, 1.0) * (1 + np.random.normal(0, 0.001))
    
    def _update_position(self, order: Order):
        """Update position after order execution"""
        symbol = order.symbol
        
        if symbol not in self.positions:
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=0.0,
                avg_price=0.0,
                market_value=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                timestamp=datetime.now(),
                strategy_id=order.strategy_id
            )
        
        position = self.positions[symbol]
        
        if order.side == OrderSide.BUY:
            new_quantity = position.quantity + order.filled_quantity
            if new_quantity != 0:
                position.avg_price = ((position.quantity * position.avg_price) + 
                                    (order.filled_quantity * order.avg_fill_price)) / new_quantity
            position.quantity = new_quantity
        else:  # SELL
            position.quantity -= order.filled_quantity
            # Calculate realized PnL
            realized_pnl = order.filled_quantity * (order.avg_fill_price - position.avg_price)
            position.realized_pnl += realized_pnl
        
        # Update market value and unrealized PnL
        current_price = self._get_market_price(symbol)
        position.market_value = position.quantity * current_price
        position.unrealized_pnl = position.quantity * (current_price - position.avg_price)
        position.timestamp = datetime.now()
    
    def get_portfolio_summary(self) -> Dict:
        """Get comprehensive portfolio summary"""
        total_value = sum(p.market_value for p in self.positions.values())
        total_unrealized = sum(p.unrealized_pnl for p in self.positions.values())
        total_realized = sum(p.realized_pnl for p in self.positions.values())
        
        return {
            'total_value': total_value,
            'total_unrealized_pnl': total_unrealized,
            'total_realized_pnl': total_realized,
            'total_pnl': total_unrealized + total_realized,
            'num_positions': len([p for p in self.positions.values() if p.quantity != 0]),
            'positions': self.positions
        }

class ProductionTradingEngine:
    """Main production trading engine"""
    
    def __init__(self, initial_capital: float = 1000000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        
        # Initialize core systems
        self.risk_manager = RiskManager()
        self.oms = OrderManagementSystem(self.risk_manager)
        self.output_manager = OutputManager("production_trading_results")
        
        # Performance tracking
        self.performance_history = []
        self.trade_history = []
        
        # Strategy registry
        self.active_strategies = {}
        
        # Logging
        self.logger = logging.getLogger('ProductionTradingEngine')
        self.logger.setLevel(logging.INFO)
        
    async def start_trading(self):
        """Start the production trading engine"""
        self.logger.info("Production Trading Engine starting...")
        
        # Initialize strategies
        await self._initialize_strategies()
        
        # Start main trading loop
        await self._main_trading_loop()
    
    async def _initialize_strategies(self):
        """Initialize trading strategies"""
        from market_making_strategy import MarketMakingStrategy
        from statistical_arbitrage_strategy import StatisticalArbitrageStrategy
        from high_frequency_momentum_strategy import HighFrequencyMomentumStrategy
        from advanced_risk_management import AdvancedRiskManager

        self.logger.info("Initializing institutional-grade trading strategies...")

        # Initialize advanced risk manager
        self.risk_manager = AdvancedRiskManager(self.initial_capital)

        # Initialize strategies
        symbols = ['BONK', 'WIF', 'POPCAT', 'FARTCOIN', 'SOL']

        # Market Making Strategy
        market_making = MarketMakingStrategy(symbols[:3], self.initial_capital * 0.4)
        await market_making.initialize(self)
        self.active_strategies['market_making'] = market_making

        # Statistical Arbitrage Strategy
        stat_arb = StatisticalArbitrageStrategy(symbols, self.initial_capital * 0.3)
        await stat_arb.initialize(self)
        self.active_strategies['statistical_arbitrage'] = stat_arb

        # High-Frequency Momentum Strategy
        hf_momentum = HighFrequencyMomentumStrategy(symbols[:2], self.initial_capital * 0.3)
        await hf_momentum.initialize(self)
        self.active_strategies['hf_momentum'] = hf_momentum

        self.logger.info(f"Initialized {len(self.active_strategies)} institutional strategies")
        
    async def _main_trading_loop(self):
        """Main trading loop"""
        while True:
            try:
                # Update market data
                await self._update_market_data()
                
                # Run strategy signals
                await self._process_strategy_signals()
                
                # Update risk metrics
                await self._update_risk_metrics()
                
                # Performance tracking
                await self._track_performance()
                
                # Sleep for next iteration (adjust based on strategy frequency)
                await asyncio.sleep(0.1)  # 100ms for high-frequency strategies
                
            except Exception as e:
                self.logger.error(f"Error in main trading loop: {e}")
                await asyncio.sleep(1)
    
    async def _update_market_data(self):
        """Update market data feeds"""
        # In production, this would connect to real market data
        pass
    
    async def _process_strategy_signals(self):
        """Process signals from all active strategies"""
        for strategy_id, strategy in self.active_strategies.items():
            try:
                signals = await strategy.generate_signals()
                for signal in signals:
                    await self._execute_signal(signal, strategy_id)
            except Exception as e:
                self.logger.error(f"Error processing strategy {strategy_id}: {e}")
    
    async def _execute_signal(self, signal: Dict, strategy_id: str):
        """Execute trading signal"""
        try:
            order = await self.oms.submit_order(
                symbol=signal['symbol'],
                side=OrderSide(signal['side']),
                order_type=OrderType(signal['order_type']),
                quantity=signal['quantity'],
                price=signal.get('price'),
                strategy_id=strategy_id
            )
            
            if order.status == OrderStatus.FILLED:
                self._log_trade(order, signal)
                
        except Exception as e:
            self.logger.error(f"Error executing signal: {e}")
    
    def _log_trade(self, order: Order, signal: Dict):
        """Log executed trade"""
        trade_record = {
            'timestamp': order.timestamp,
            'order_id': order.order_id,
            'symbol': order.symbol,
            'side': order.side.value,
            'quantity': order.filled_quantity,
            'price': order.avg_fill_price,
            'value': order.filled_quantity * order.avg_fill_price,
            'strategy_id': order.strategy_id,
            'signal_strength': signal.get('strength', 0),
            'execution_latency_ms': self.oms.execution_latency_ms
        }
        
        self.trade_history.append(trade_record)
        self.output_manager.log_trade(trade_record, order.strategy_id)
    
    async def _update_risk_metrics(self):
        """Update risk metrics"""
        portfolio = self.oms.get_portfolio_summary()
        
        if len(self.performance_history) > 30:
            returns = np.array([p['daily_return'] for p in self.performance_history[-252:]])
            var_95 = self.risk_manager.calculate_var(returns, 0.95)
            es_95 = self.risk_manager.calculate_expected_shortfall(returns, 0.95)
            
            # Stress testing
            stress_results = {}
            for scenario in self.risk_manager.stress_scenarios:
                stress_results[scenario] = self.risk_manager.stress_test_portfolio(
                    self.oms.positions, scenario
                )
            
            risk_metrics = {
                'timestamp': datetime.now(),
                'var_95': var_95,
                'expected_shortfall_95': es_95,
                'portfolio_value': portfolio['total_value'],
                'total_pnl': portfolio['total_pnl'],
                'stress_test_results': stress_results
            }
            
            self.output_manager.log_gmgn_data(risk_metrics, 'risk_metrics')
    
    async def _track_performance(self):
        """Track performance metrics"""
        portfolio = self.oms.get_portfolio_summary()
        
        performance_snapshot = {
            'timestamp': datetime.now(),
            'portfolio_value': portfolio['total_value'],
            'total_pnl': portfolio['total_pnl'],
            'daily_return': 0.0,  # Calculate based on previous day
            'num_positions': portfolio['num_positions'],
            'capital_utilization': portfolio['total_value'] / self.initial_capital
        }
        
        self.performance_history.append(performance_snapshot)
        
        # Calculate performance metrics
        if len(self.performance_history) > 1:
            prev_value = self.performance_history[-2]['portfolio_value']
            performance_snapshot['daily_return'] = (portfolio['total_value'] - prev_value) / prev_value
    
    def get_performance_metrics(self) -> Dict:
        """Calculate comprehensive performance metrics"""
        if len(self.performance_history) < 2:
            return {}
        
        returns = np.array([p['daily_return'] for p in self.performance_history[1:]])
        
        total_return = (self.performance_history[-1]['portfolio_value'] - self.initial_capital) / self.initial_capital
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Calculate maximum drawdown
        portfolio_values = [p['portfolio_value'] for p in self.performance_history]
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown)
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'volatility': np.std(returns) * np.sqrt(252),
            'win_rate': len([r for r in returns if r > 0]) / len(returns) if returns.size > 0 else 0,
            'total_trades': len(self.trade_history),
            'avg_daily_return': np.mean(returns),
            'current_portfolio_value': self.performance_history[-1]['portfolio_value']
        }

if __name__ == "__main__":
    async def main():
        engine = ProductionTradingEngine(initial_capital=1000000)
        await engine.start_trading()
    
    # asyncio.run(main())
