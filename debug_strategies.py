"""
Debug script to understand why strategies aren't executing trades
"""

from data_analysis import <PERSON>Analyzer
from momentum_strategy import MomentumStrategy
from mean_reversion_strategy import MeanReversionStrategy
import pandas as pd

def debug_momentum_strategy():
    print("="*50)
    print("DEBUGGING MOMENTUM STRATEGY")
    print("="*50)
    
    # Load data
    analyzer = DataAnalyzer("Quant test.csv")
    analyzer.load_data()
    analyzer.parse_trading_history()
    price_data = analyzer.create_price_series()
    
    # Initialize strategy
    strategy = MomentumStrategy()
    
    # Test a few tokens
    test_tokens = list(price_data.keys())[:10]
    
    for token in test_tokens:
        df = price_data[token]
        if len(df) >= 3:
            print(f"\nTesting token: {token}")
            print(f"  Data points: {len(df)}")
            print(f"  Price range: ${df['price'].min():.6f} - ${df['price'].max():.6f}")
            
            # Test momentum calculation
            momentum_score = strategy.calculate_momentum_score(df)
            print(f"  Momentum score: {momentum_score:.4f} (threshold: {strategy.momentum_threshold})")
            
            # Test entry conditions
            current_time = df['datetime'].iloc[-1]
            should_enter = strategy.should_enter_position(token, df, current_time)
            print(f"  Should enter: {should_enter}")
            
            # Check individual conditions
            latest_data = df.iloc[-1]
            liquidity = latest_data.get('liquidity', 0)
            print(f"  Liquidity: {liquidity} (min required: {strategy.min_liquidity})")
            print(f"  Momentum > threshold: {momentum_score > strategy.momentum_threshold}")

def debug_mean_reversion_strategy():
    print("\n" + "="*50)
    print("DEBUGGING MEAN REVERSION STRATEGY")
    print("="*50)
    
    # Load data
    analyzer = DataAnalyzer("Quant test.csv")
    analyzer.load_data()
    analyzer.parse_trading_history()
    price_data = analyzer.create_price_series()
    
    # Initialize strategy
    strategy = MeanReversionStrategy()
    
    # Test a few tokens
    test_tokens = list(price_data.keys())[:10]
    
    for token in test_tokens:
        df = price_data[token]
        if len(df) >= 5:
            print(f"\nTesting token: {token}")
            print(f"  Data points: {len(df)}")
            print(f"  Price range: ${df['price'].min():.6f} - ${df['price'].max():.6f}")
            
            # Test mean reversion calculation
            z_score, price_deviation, volatility = strategy.calculate_mean_reversion_score(df)
            print(f"  Z-score: {z_score:.4f}")
            print(f"  Price deviation: {price_deviation:.4f} (threshold: {strategy.oversold_threshold})")
            print(f"  Volatility: {volatility:.4f} (min required: {strategy.volatility_threshold})")
            
            # Test entry conditions
            current_time = df['datetime'].iloc[-1]
            should_enter = strategy.should_enter_position(token, df, current_time)
            print(f"  Should enter: {should_enter}")
            
            # Check individual conditions
            latest_data = df.iloc[-1]
            liquidity = latest_data.get('liquidity', 0)
            print(f"  Liquidity: {liquidity} (min required: {strategy.min_liquidity})")
            print(f"  Oversold condition: {price_deviation < strategy.oversold_threshold and z_score < -1.5}")

if __name__ == "__main__":
    debug_momentum_strategy()
    debug_mean_reversion_strategy()
