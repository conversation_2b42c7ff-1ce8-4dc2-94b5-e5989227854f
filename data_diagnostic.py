"""
Data Diagnostic Script
Quick analysis to understand the data structure and trading opportunities
"""

import pandas as pd
import numpy as np
from data_analysis import DataAnalyzer

def main():
    print("="*60)
    print("DATA DIAGNOSTIC ANALYSIS")
    print("="*60)
    
    # Initialize analyzer
    analyzer = DataAnalyzer("Quant test.csv")
    
    # Load and parse data
    analyzer.load_data()
    analyzer.parse_trading_history()
    price_data = analyzer.create_price_series()
    
    print(f"\nPrice Data Analysis:")
    print(f"Total tokens with price series: {len(price_data)}")
    
    # Analyze a few sample tokens
    sample_tokens = list(price_data.keys())[:5]
    
    for token in sample_tokens:
        df = price_data[token]
        print(f"\nToken: {token}")
        print(f"  Data points: {len(df)}")
        print(f"  Price range: ${df['price'].min():.6f} - ${df['price'].max():.6f}")
        print(f"  Date range: {df['datetime'].min()} to {df['datetime'].max()}")
        
        # Calculate basic statistics
        if len(df) > 1:
            returns = df['price'].pct_change().dropna()
            if len(returns) > 0:
                print(f"  Avg return: {returns.mean()*100:.2f}%")
                print(f"  Volatility: {returns.std()*100:.2f}%")
                print(f"  Max gain: {returns.max()*100:.2f}%")
                print(f"  Max loss: {returns.min()*100:.2f}%")
    
    # Check for trading opportunities
    print(f"\n" + "="*40)
    print("TRADING OPPORTUNITY ANALYSIS")
    print("="*40)
    
    momentum_opportunities = 0
    mean_reversion_opportunities = 0
    
    for token, df in price_data.items():
        if len(df) >= 5:
            # Check momentum opportunities
            recent_prices = df['price'].tail(3)
            if len(recent_prices) >= 2:
                momentum = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
                if momentum > 0.02:  # 2% momentum
                    momentum_opportunities += 1
            
            # Check mean reversion opportunities
            if len(df) >= 5:
                mean_price = df['price'].tail(5).mean()
                current_price = df['price'].iloc[-1]
                deviation = (current_price - mean_price) / mean_price
                if deviation < -0.10:  # 10% below mean
                    mean_reversion_opportunities += 1
    
    print(f"Momentum opportunities: {momentum_opportunities}")
    print(f"Mean reversion opportunities: {mean_reversion_opportunities}")
    print(f"Total tokens analyzed: {len(price_data)}")
    
    # Sample some high-activity tokens
    print(f"\n" + "="*40)
    print("HIGH-ACTIVITY TOKENS")
    print("="*40)
    
    token_activity = [(token, len(df)) for token, df in price_data.items()]
    token_activity.sort(key=lambda x: x[1], reverse=True)
    
    for token, count in token_activity[:10]:
        df = price_data[token]
        print(f"{token}: {count} data points, price range ${df['price'].min():.6f}-${df['price'].max():.6f}")

if __name__ == "__main__":
    main()
