# Quantitative Trading Strategy Requirements
# Core data analysis and numerical computing
pandas>=1.5.0
numpy>=1.21.0

# Visualization and plotting
matplotlib>=3.5.0
seaborn>=0.11.0

# Statistical analysis and machine learning
scipy>=1.9.0
scikit-learn>=1.1.0

# Date and time handling
python-dateutil>=2.8.0

# JSON handling (built-in, but ensuring compatibility)
# json - built-in module

# Optional: Enhanced performance and additional features
# numba>=0.56.0  # For numerical acceleration
# plotly>=5.0.0  # For interactive plots
# jupyter>=1.0.0  # For notebook analysis

# Development and testing (optional)
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0
