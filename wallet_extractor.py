"""
Wallet Address Extractor
Extract all unique wallet addresses from Quant test.csv for multi-wallet GMGN.ai integration
"""

import pandas as pd
import re
from typing import Set, List, Dict
import logging

class WalletExtractor:
    """Extract and analyze wallet addresses from CSV data"""
    
    def __init__(self, csv_file: str = "Quant test.csv"):
        self.csv_file = csv_file
        self.logger = logging.getLogger(__name__)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        
        self.wallet_addresses = set()
        self.wallet_stats = {}
        
    def is_valid_solana_address(self, address: str) -> bool:
        """Check if string is a valid Solana wallet address"""
        if not address or not isinstance(address, str):
            return False
            
        # Solana addresses are base58 encoded, 32-44 characters long
        if len(address) < 32 or len(address) > 44:
            return False
            
        # Check for valid base58 characters (no 0, O, I, l)
        base58_pattern = r'^[1-9A-HJ-NP-Za-km-z]+$'
        if not re.match(base58_pattern, address):
            return False
            
        return True
    
    def extract_addresses_from_csv(self) -> Set[str]:
        """Extract all wallet addresses from the CSV file"""
        
        try:
            self.logger.info(f"Loading data from {self.csv_file}")
            df = pd.read_csv(self.csv_file)
            
            self.logger.info(f"Loaded {len(df)} rows with columns: {list(df.columns)}")
            
            # Look for wallet addresses in all columns
            potential_addresses = set()
            
            for column in df.columns:
                self.logger.info(f"Scanning column: {column}")
                
                # Convert column to string and extract potential addresses
                for value in df[column].astype(str):
                    if pd.isna(value) or value == 'nan':
                        continue
                        
                    # Split by common delimiters and check each part
                    parts = re.split(r'[,;\s\n\r\t]+', str(value))
                    
                    for part in parts:
                        part = part.strip()
                        if self.is_valid_solana_address(part):
                            potential_addresses.add(part)
            
            # Filter out obviously invalid addresses
            valid_addresses = set()
            for addr in potential_addresses:
                # Additional validation - exclude common false positives
                if (len(addr) >= 32 and 
                    not addr.isdigit() and 
                    not addr.startswith('http') and
                    not addr.endswith('.com')):
                    valid_addresses.add(addr)
            
            self.wallet_addresses = valid_addresses
            self.logger.info(f"Found {len(self.wallet_addresses)} unique wallet addresses")
            
            return self.wallet_addresses
            
        except Exception as e:
            self.logger.error(f"Error extracting addresses: {e}")
            return set()
    
    def analyze_wallet_activity(self, df: pd.DataFrame) -> Dict:
        """Analyze wallet activity and performance"""
        
        wallet_stats = {}
        
        for address in self.wallet_addresses:
            stats = {
                'address': address,
                'total_transactions': 0,
                'total_volume': 0.0,
                'unique_tokens': set(),
                'first_seen': None,
                'last_seen': None,
                'estimated_pnl': 0.0
            }
            
            # Search for this address in all columns
            for column in df.columns:
                mask = df[column].astype(str).str.contains(address, na=False)
                if mask.any():
                    address_rows = df[mask]
                    
                    stats['total_transactions'] += len(address_rows)
                    
                    # Try to extract volume and token information
                    for _, row in address_rows.iterrows():
                        # Look for volume/value columns
                        for col in ['volume', 'value', 'amount', 'usd_value']:
                            if col in df.columns:
                                try:
                                    value = float(row[col])
                                    stats['total_volume'] += value
                                except:
                                    pass
                        
                        # Look for token symbols
                        for col in ['symbol', 'token', 'token_symbol']:
                            if col in df.columns and pd.notna(row[col]):
                                stats['unique_tokens'].add(str(row[col]))
                        
                        # Look for timestamps
                        for col in ['timestamp', 'date', 'time']:
                            if col in df.columns and pd.notna(row[col]):
                                try:
                                    timestamp = pd.to_datetime(row[col])
                                    if stats['first_seen'] is None or timestamp < stats['first_seen']:
                                        stats['first_seen'] = timestamp
                                    if stats['last_seen'] is None or timestamp > stats['last_seen']:
                                        stats['last_seen'] = timestamp
                                except:
                                    pass
            
            stats['unique_tokens'] = list(stats['unique_tokens'])
            wallet_stats[address] = stats
        
        self.wallet_stats = wallet_stats
        return wallet_stats
    
    def get_top_wallets(self, n: int = 10, sort_by: str = 'total_volume') -> List[Dict]:
        """Get top N wallets sorted by specified metric"""
        
        if not self.wallet_stats:
            return []
        
        sorted_wallets = sorted(
            self.wallet_stats.values(),
            key=lambda x: x.get(sort_by, 0),
            reverse=True
        )
        
        return sorted_wallets[:n]
    
    def generate_wallet_report(self) -> str:
        """Generate comprehensive wallet analysis report"""
        
        if not self.wallet_addresses:
            return "No wallet addresses found."
        
        report = []
        report.append("="*80)
        report.append("WALLET ADDRESS ANALYSIS REPORT")
        report.append("="*80)
        report.append(f"Total Unique Addresses Found: {len(self.wallet_addresses)}")
        report.append("")
        
        if self.wallet_stats:
            # Summary statistics
            total_transactions = sum(stats['total_transactions'] for stats in self.wallet_stats.values())
            total_volume = sum(stats['total_volume'] for stats in self.wallet_stats.values())
            
            report.append("SUMMARY STATISTICS:")
            report.append(f"Total Transactions: {total_transactions:,}")
            report.append(f"Total Volume: ${total_volume:,.2f}")
            report.append("")
            
            # Top wallets by volume
            top_wallets = self.get_top_wallets(10, 'total_volume')
            report.append("TOP 10 WALLETS BY VOLUME:")
            report.append("-" * 80)
            report.append(f"{'Rank':<4} {'Address':<45} {'Volume':<15} {'Transactions':<12}")
            report.append("-" * 80)
            
            for i, wallet in enumerate(top_wallets, 1):
                report.append(f"{i:<4} {wallet['address']:<45} ${wallet['total_volume']:<14,.2f} {wallet['total_transactions']:<12}")
            
            report.append("")
            
            # Top wallets by transaction count
            top_wallets_tx = self.get_top_wallets(10, 'total_transactions')
            report.append("TOP 10 WALLETS BY TRANSACTION COUNT:")
            report.append("-" * 80)
            report.append(f"{'Rank':<4} {'Address':<45} {'Transactions':<12} {'Volume':<15}")
            report.append("-" * 80)
            
            for i, wallet in enumerate(top_wallets_tx, 1):
                report.append(f"{i:<4} {wallet['address']:<45} {wallet['total_transactions']:<12} ${wallet['total_volume']:<14,.2f}")
        
        else:
            report.append("WALLET ADDRESSES FOUND:")
            report.append("-" * 80)
            for i, address in enumerate(sorted(self.wallet_addresses), 1):
                report.append(f"{i:3d}. {address}")
        
        return "\n".join(report)
    
    def save_addresses_to_file(self, filename: str = "wallet_addresses.txt"):
        """Save all wallet addresses to a text file"""
        
        try:
            with open(filename, 'w') as f:
                f.write("# Extracted Wallet Addresses from Quant test.csv\n")
                f.write(f"# Total addresses: {len(self.wallet_addresses)}\n")
                f.write("# Generated by WalletExtractor\n\n")
                
                for address in sorted(self.wallet_addresses):
                    f.write(f"{address}\n")
            
            self.logger.info(f"Saved {len(self.wallet_addresses)} addresses to {filename}")
            
        except Exception as e:
            self.logger.error(f"Error saving addresses: {e}")

def main():
    """Main execution function"""
    
    extractor = WalletExtractor()
    
    # Extract addresses
    addresses = extractor.extract_addresses_from_csv()
    
    if addresses:
        # Load data for analysis
        try:
            df = pd.read_csv("Quant test.csv")
            extractor.analyze_wallet_activity(df)
        except Exception as e:
            print(f"Could not analyze wallet activity: {e}")
        
        # Generate and print report
        report = extractor.generate_wallet_report()
        print(report)
        
        # Save addresses to file
        extractor.save_addresses_to_file()
        
        # Save detailed report
        with open("wallet_analysis_report.txt", "w") as f:
            f.write(report)
        
        print(f"\nFiles generated:")
        print(f"- wallet_addresses.txt ({len(addresses)} addresses)")
        print(f"- wallet_analysis_report.txt (detailed analysis)")
        
    else:
        print("No wallet addresses found in the CSV file.")

if __name__ == "__main__":
    main()
