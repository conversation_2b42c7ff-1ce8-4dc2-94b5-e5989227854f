"""
Momentum Trading Strategy
Focuses on tokens showing strong price trends and momentum patterns
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class MomentumStrategy:
    def __init__(self, initial_capital=100000, max_positions=10, position_size=0.1):
        """
        Initialize Momentum Strategy
        
        Args:
            initial_capital: Starting capital in USD
            max_positions: Maximum number of concurrent positions
            position_size: Fraction of capital per position (0.1 = 10%)
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_positions = max_positions
        self.position_size = position_size
        self.positions = {}  # Current positions
        self.trade_history = []
        self.performance_metrics = {}
        
        # Strategy parameters (adjusted for crypto volatility)
        self.momentum_threshold = 0.02  # 2% price increase threshold (more sensitive)
        self.stop_loss = 0.10  # 10% stop loss
        self.take_profit = 0.20  # 20% take profit
        self.min_liquidity = 100  # Minimum liquidity requirement (lowered)
        self.lookback_period = 3  # Days to look back for momentum (shorter for crypto)
        
    def calculate_momentum_score(self, price_data):
        """Calculate momentum score for a token"""
        if len(price_data) < self.lookback_period:
            return 0
        
        # Price momentum over lookback period
        recent_prices = price_data['price'].tail(self.lookback_period)
        price_momentum = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
        
        # Volume momentum (using USD value as proxy)
        recent_volumes = price_data['usd_value'].tail(self.lookback_period)
        volume_momentum = recent_volumes.mean() / (recent_volumes.std() + 1e-8)
        
        # Trend consistency (how many consecutive positive returns)
        returns = recent_prices.pct_change().dropna()
        positive_streak = 0
        for ret in returns.iloc[::-1]:  # Reverse order
            if ret > 0:
                positive_streak += 1
            else:
                break
        
        trend_consistency = positive_streak / len(returns) if len(returns) > 0 else 0
        
        # Combined momentum score
        momentum_score = (
            price_momentum * 0.5 +
            np.tanh(volume_momentum) * 0.3 +
            trend_consistency * 0.2
        )
        
        return momentum_score
    
    def should_enter_position(self, token_symbol, price_data, current_time):
        """Determine if we should enter a position"""
        # Check if we already have this position
        if token_symbol in self.positions:
            return False
        
        # Check if we have room for more positions
        if len(self.positions) >= self.max_positions:
            return False
        
        # Check minimum data requirements
        if len(price_data) < self.lookback_period:
            return False
        
        # Check liquidity requirement
        latest_data = price_data.iloc[-1]
        if latest_data.get('liquidity', 0) < self.min_liquidity:
            return False
        
        # Calculate momentum score
        momentum_score = self.calculate_momentum_score(price_data)
        
        # Entry condition: strong positive momentum
        if momentum_score > self.momentum_threshold:
            return True
        
        return False
    
    def should_exit_position(self, token_symbol, current_price, current_time):
        """Determine if we should exit a position"""
        if token_symbol not in self.positions:
            return False
        
        position = self.positions[token_symbol]
        entry_price = position['entry_price']
        
        # Calculate current return
        current_return = (current_price - entry_price) / entry_price
        
        # Exit conditions
        # 1. Stop loss
        if current_return <= -self.stop_loss:
            return True, "stop_loss"
        
        # 2. Take profit
        if current_return >= self.take_profit:
            return True, "take_profit"
        
        # 3. Time-based exit (hold for maximum 30 days)
        days_held = (current_time - position['entry_time']).days
        if days_held >= 30:
            return True, "time_exit"
        
        # 4. Momentum reversal (negative momentum for 3 consecutive periods)
        # This would require recent price data, simplified for now
        
        return False, None
    
    def enter_position(self, token_symbol, price, timestamp, token_data):
        """Enter a new position"""
        position_value = self.current_capital * self.position_size
        shares = position_value / price
        
        position = {
            'symbol': token_symbol,
            'entry_price': price,
            'entry_time': timestamp,
            'shares': shares,
            'position_value': position_value,
            'entry_data': token_data
        }
        
        self.positions[token_symbol] = position
        self.current_capital -= position_value
        
        # Record trade
        trade = {
            'timestamp': timestamp,
            'symbol': token_symbol,
            'action': 'BUY',
            'price': price,
            'shares': shares,
            'value': position_value,
            'capital_remaining': self.current_capital
        }
        self.trade_history.append(trade)
        
        return position
    
    def exit_position(self, token_symbol, price, timestamp, exit_reason):
        """Exit an existing position"""
        if token_symbol not in self.positions:
            return None
        
        position = self.positions[token_symbol]
        shares = position['shares']
        exit_value = shares * price
        
        # Calculate profit/loss
        profit_loss = exit_value - position['position_value']
        return_pct = profit_loss / position['position_value']
        
        # Update capital
        self.current_capital += exit_value
        
        # Record trade
        trade = {
            'timestamp': timestamp,
            'symbol': token_symbol,
            'action': 'SELL',
            'price': price,
            'shares': shares,
            'value': exit_value,
            'profit_loss': profit_loss,
            'return_pct': return_pct,
            'exit_reason': exit_reason,
            'capital_remaining': self.current_capital,
            'days_held': (timestamp - position['entry_time']).days
        }
        self.trade_history.append(trade)
        
        # Remove position
        del self.positions[token_symbol]
        
        return trade
    
    def run_backtest(self, price_data_dict, start_date=None, end_date=None):
        """Run the momentum strategy backtest"""
        print("Running Momentum Strategy Backtest...")
        
        # Create timeline of all price updates
        all_updates = []
        for token, data in price_data_dict.items():
            for _, row in data.iterrows():
                all_updates.append({
                    'timestamp': row['datetime'],
                    'token': token,
                    'price': row['price'],
                    'data': row
                })
        
        # Sort by timestamp
        all_updates = sorted(all_updates, key=lambda x: x['timestamp'])
        
        # Filter by date range if specified
        if start_date:
            all_updates = [u for u in all_updates if u['timestamp'] >= start_date]
        if end_date:
            all_updates = [u for u in all_updates if u['timestamp'] <= end_date]
        
        print(f"Processing {len(all_updates)} price updates...")
        
        # Process each update
        for i, update in enumerate(all_updates):
            token = update['token']
            price = update['price']
            timestamp = update['timestamp']
            
            # Get historical data for this token up to current time
            token_history = price_data_dict[token]
            current_history = token_history[token_history['datetime'] <= timestamp]
            
            # Check exit conditions for existing positions
            if token in self.positions:
                should_exit, exit_reason = self.should_exit_position(token, price, timestamp)
                if should_exit:
                    self.exit_position(token, price, timestamp, exit_reason)
            
            # Check entry conditions
            elif self.should_enter_position(token, current_history, timestamp):
                self.enter_position(token, price, timestamp, update['data'])
            
            # Progress update
            if i % 1000 == 0:
                print(f"Processed {i}/{len(all_updates)} updates. Current capital: ${self.current_capital:.2f}")
        
        # Close any remaining positions at the end
        final_timestamp = all_updates[-1]['timestamp'] if all_updates else datetime.now()
        for token in list(self.positions.keys()):
            final_price = price_data_dict[token].iloc[-1]['price']
            self.exit_position(token, final_price, final_timestamp, "backtest_end")
        
        print(f"Backtest complete. Final capital: ${self.current_capital:.2f}")
        return self.calculate_performance_metrics()
    
    def calculate_performance_metrics(self):
        """Calculate comprehensive performance metrics"""
        if not self.trade_history:
            return {}
        
        trades_df = pd.DataFrame(self.trade_history)
        
        # Filter completed trades (buy-sell pairs)
        sell_trades = trades_df[trades_df['action'] == 'SELL'].copy()
        
        if len(sell_trades) == 0:
            return {'error': 'No completed trades'}
        
        # Basic metrics
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        total_trades = len(sell_trades)
        winning_trades = len(sell_trades[sell_trades['profit_loss'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Profit metrics
        avg_profit = sell_trades['profit_loss'].mean()
        total_profit = sell_trades['profit_loss'].sum()
        avg_return = sell_trades['return_pct'].mean()
        
        # Risk metrics
        returns = sell_trades['return_pct']
        volatility = returns.std()
        sharpe_ratio = (avg_return / volatility) if volatility > 0 else 0
        
        # Maximum drawdown calculation
        capital_series = trades_df['capital_remaining'].tolist()
        capital_series.insert(0, self.initial_capital)
        peak = capital_series[0]
        max_drawdown = 0
        
        for capital in capital_series:
            if capital > peak:
                peak = capital
            drawdown = (peak - capital) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        self.performance_metrics = {
            'total_return': total_return,
            'total_profit': total_profit,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_profit_per_trade': avg_profit,
            'avg_return_per_trade': avg_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'final_capital': self.current_capital,
            'avg_days_held': sell_trades['days_held'].mean()
        }
        
        return self.performance_metrics
    
    def print_performance_report(self):
        """Print a detailed performance report"""
        if not self.performance_metrics:
            self.calculate_performance_metrics()
        
        metrics = self.performance_metrics
        
        print("\n" + "="*50)
        print("MOMENTUM STRATEGY PERFORMANCE REPORT")
        print("="*50)
        
        print(f"Initial Capital: ${self.initial_capital:,.2f}")
        print(f"Final Capital: ${metrics.get('final_capital', 0):,.2f}")
        print(f"Total Return: {metrics.get('total_return', 0)*100:.2f}%")
        print(f"Total Profit: ${metrics.get('total_profit', 0):,.2f}")
        
        print(f"\nTrading Statistics:")
        print(f"Total Trades: {metrics.get('total_trades', 0)}")
        print(f"Win Rate: {metrics.get('win_rate', 0)*100:.1f}%")
        print(f"Average Profit per Trade: ${metrics.get('avg_profit_per_trade', 0):.2f}")
        print(f"Average Return per Trade: {metrics.get('avg_return_per_trade', 0)*100:.2f}%")
        print(f"Average Days Held: {metrics.get('avg_days_held', 0):.1f}")
        
        print(f"\nRisk Metrics:")
        print(f"Volatility: {metrics.get('volatility', 0)*100:.2f}%")
        print(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.3f}")
        print(f"Maximum Drawdown: {metrics.get('max_drawdown', 0)*100:.2f}%")
        
        return metrics
