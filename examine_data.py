import pandas as pd
import json

# Load data
df = pd.read_csv('Quant test.csv')

# Find non-empty history_trade entries
non_empty = df[(df['history_trade'].notna()) & (df['history_trade'] != '[]')]
print(f"Non-empty history_trade entries: {len(non_empty)}")

if len(non_empty) > 0:
    # Examine the first few entries
    for i in range(min(3, len(non_empty))):
        print(f"\n--- Entry {i+1} ---")
        history_data = non_empty.iloc[i]['history_trade']
        print(f"Raw data length: {len(history_data)}")
        print(f"First 500 chars: {history_data[:500]}")
        
        try:
            parsed = json.loads(history_data)
            print(f"Parsed JSON type: {type(parsed)}")
            if isinstance(parsed, list) and len(parsed) > 0:
                print(f"List length: {len(parsed)}")
                print(f"First item keys: {list(parsed[0].keys()) if isinstance(parsed[0], dict) else 'Not a dict'}")
                if isinstance(parsed[0], dict) and 'token' in parsed[0]:
                    token_info = parsed[0]['token']
                    print(f"Token keys: {list(token_info.keys()) if isinstance(token_info, dict) else 'Not a dict'}")
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
