"""
Statistical Arbitrage Strategy
Advanced pairs trading and mean reversion across correlated assets
Target: >65% win rate, Sharpe ratio >2.5, <3% max drawdown
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
import logging
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler

from production_trading_engine import OrderSide, OrderType, ProductionTradingEngine

@dataclass
class PairRelationship:
    """Statistical relationship between two assets"""
    symbol_a: str
    symbol_b: str
    correlation: float
    cointegration_pvalue: float
    hedge_ratio: float
    half_life: float  # Mean reversion half-life in minutes
    spread_mean: float
    spread_std: float
    last_updated: datetime

class CointegrationAnalyzer:
    """Advanced cointegration analysis for pairs trading"""
    
    def __init__(self, lookback_period: int = 252):
        self.lookback_period = lookback_period
        self.min_correlation = 0.7
        self.max_pvalue = 0.05  # 5% significance level for cointegration
        
    def analyze_pair(self, prices_a: np.ndarray, prices_b: np.ndarray) -> Optional[PairRelationship]:
        """Analyze statistical relationship between two price series"""
        if len(prices_a) < 30 or len(prices_b) < 30:
            return None
        
        # Calculate correlation
        correlation = np.corrcoef(prices_a, prices_b)[0, 1]
        
        if abs(correlation) < self.min_correlation:
            return None
        
        # Perform cointegration test (Engle-Granger)
        try:
            # Step 1: Run regression to find hedge ratio
            X = prices_a.reshape(-1, 1)
            y = prices_b
            
            reg = LinearRegression().fit(X, y)
            hedge_ratio = reg.coef_[0]
            
            # Step 2: Calculate spread
            spread = prices_b - hedge_ratio * prices_a
            
            # Step 3: Test spread for stationarity (ADF test)
            from statsmodels.tsa.stattools import adfuller
            adf_result = adfuller(spread, maxlag=1)
            pvalue = adf_result[1]
            
            if pvalue > self.max_pvalue:
                return None
            
            # Calculate spread statistics
            spread_mean = np.mean(spread)
            spread_std = np.std(spread)
            
            # Calculate half-life of mean reversion
            half_life = self._calculate_half_life(spread)
            
            return PairRelationship(
                symbol_a=f"ASSET_A",  # Will be set by caller
                symbol_b=f"ASSET_B",  # Will be set by caller
                correlation=correlation,
                cointegration_pvalue=pvalue,
                hedge_ratio=hedge_ratio,
                half_life=half_life,
                spread_mean=spread_mean,
                spread_std=spread_std,
                last_updated=datetime.now()
            )
            
        except Exception as e:
            logging.error(f"Error in cointegration analysis: {e}")
            return None
    
    def _calculate_half_life(self, spread: np.ndarray) -> float:
        """Calculate mean reversion half-life using Ornstein-Uhlenbeck process"""
        try:
            # Fit AR(1) model: spread[t] = alpha + beta * spread[t-1] + epsilon[t]
            y = spread[1:]
            x = spread[:-1]
            
            # Add constant term
            X = np.column_stack([np.ones(len(x)), x])
            
            # OLS regression
            coeffs = np.linalg.lstsq(X, y, rcond=None)[0]
            beta = coeffs[1]
            
            # Half-life calculation
            if beta >= 1 or beta <= 0:
                return float('inf')  # No mean reversion
            
            half_life = -np.log(2) / np.log(beta)
            return max(1, min(half_life, 1440))  # Clamp between 1 minute and 1 day
            
        except Exception:
            return 60.0  # Default 1 hour

class StatisticalArbitrageStrategy:
    """Advanced statistical arbitrage strategy"""
    
    def __init__(self, symbols: List[str], initial_capital: float = 1000000):
        self.symbols = symbols
        self.initial_capital = initial_capital
        self.strategy_id = "statistical_arbitrage_v1"
        
        # Strategy parameters
        self.entry_threshold = 2.0  # Z-score threshold for entry
        self.exit_threshold = 0.5   # Z-score threshold for exit
        self.stop_loss_threshold = 4.0  # Z-score stop loss
        self.max_position_size = 100000  # Maximum position per pair
        self.max_pairs = 10  # Maximum number of active pairs
        
        # Analysis components
        self.cointegration_analyzer = CointegrationAnalyzer()
        
        # Data storage
        self.price_history: Dict[str, List[float]] = {symbol: [] for symbol in symbols}
        self.pair_relationships: Dict[Tuple[str, str], PairRelationship] = {}
        self.active_pairs: Set[Tuple[str, str]] = set()
        
        # Position tracking
        self.pair_positions: Dict[Tuple[str, str], Dict] = {}
        
        # Performance tracking
        self.trades_executed = 0
        self.total_pnl = 0.0
        self.daily_returns = []
        self.win_count = 0
        self.loss_count = 0
        
        # Risk management
        self.max_daily_loss = initial_capital * 0.02  # 2% daily loss limit
        self.current_daily_pnl = 0.0
        
        # Logging
        self.logger = logging.getLogger(f'StatArb_{self.strategy_id}')
        
    async def initialize(self, trading_engine: ProductionTradingEngine):
        """Initialize strategy with trading engine"""
        self.trading_engine = trading_engine
        self.logger.info(f"Statistical Arbitrage Strategy initialized for {len(self.symbols)} symbols")
        
        # Initial pair discovery
        await self._discover_pairs()
        
    async def _discover_pairs(self):
        """Discover cointegrated pairs from available symbols"""
        self.logger.info("Discovering cointegrated pairs...")
        
        # Need sufficient price history for analysis
        min_history = 100
        valid_symbols = [
            symbol for symbol in self.symbols 
            if len(self.price_history[symbol]) >= min_history
        ]
        
        pairs_found = 0
        
        for i, symbol_a in enumerate(valid_symbols):
            for symbol_b in valid_symbols[i+1:]:
                prices_a = np.array(self.price_history[symbol_a][-self.cointegration_analyzer.lookback_period:])
                prices_b = np.array(self.price_history[symbol_b][-self.cointegration_analyzer.lookback_period:])
                
                relationship = self.cointegration_analyzer.analyze_pair(prices_a, prices_b)
                
                if relationship:
                    relationship.symbol_a = symbol_a
                    relationship.symbol_b = symbol_b
                    
                    pair_key = (symbol_a, symbol_b)
                    self.pair_relationships[pair_key] = relationship
                    pairs_found += 1
                    
                    self.logger.info(
                        f"Found cointegrated pair: {symbol_a}-{symbol_b} "
                        f"(corr: {relationship.correlation:.3f}, "
                        f"p-value: {relationship.cointegration_pvalue:.4f}, "
                        f"half-life: {relationship.half_life:.1f}min)"
                    )
        
        self.logger.info(f"Discovered {pairs_found} cointegrated pairs")
    
    async def generate_signals(self) -> List[Dict]:
        """Generate statistical arbitrage signals"""
        signals = []
        
        # Update pair relationships periodically
        if len(self.price_history[self.symbols[0]]) % 50 == 0:  # Every 50 data points
            await self._discover_pairs()
        
        # Generate signals for each pair
        for pair_key, relationship in self.pair_relationships.items():
            try:
                pair_signals = await self._generate_pair_signals(pair_key, relationship)
                signals.extend(pair_signals)
            except Exception as e:
                self.logger.error(f"Error generating signals for pair {pair_key}: {e}")
        
        return signals
    
    async def _generate_pair_signals(self, pair_key: Tuple[str, str], 
                                   relationship: PairRelationship) -> List[Dict]:
        """Generate signals for a specific pair"""
        symbol_a, symbol_b = pair_key
        
        # Get current prices
        if (len(self.price_history[symbol_a]) == 0 or 
            len(self.price_history[symbol_b]) == 0):
            return []
        
        price_a = self.price_history[symbol_a][-1]
        price_b = self.price_history[symbol_b][-1]
        
        # Calculate current spread
        current_spread = price_b - relationship.hedge_ratio * price_a
        
        # Calculate z-score
        z_score = (current_spread - relationship.spread_mean) / relationship.spread_std
        
        signals = []
        
        # Check if we have an existing position
        existing_position = self.pair_positions.get(pair_key)
        
        if existing_position:
            # Manage existing position
            signals.extend(self._manage_existing_position(pair_key, z_score, existing_position))
        else:
            # Look for new entry opportunities
            signals.extend(self._check_entry_signals(pair_key, z_score, relationship))
        
        return signals
    
    def _manage_existing_position(self, pair_key: Tuple[str, str], z_score: float, 
                                position: Dict) -> List[Dict]:
        """Manage existing pair position"""
        signals = []
        symbol_a, symbol_b = pair_key
        
        position_type = position['type']  # 'long_spread' or 'short_spread'
        entry_z_score = position['entry_z_score']
        
        # Exit conditions
        should_exit = False
        exit_reason = ""
        
        # Mean reversion exit
        if position_type == 'long_spread' and z_score <= self.exit_threshold:
            should_exit = True
            exit_reason = "mean_reversion_exit"
        elif position_type == 'short_spread' and z_score >= -self.exit_threshold:
            should_exit = True
            exit_reason = "mean_reversion_exit"
        
        # Stop loss exit
        if position_type == 'long_spread' and z_score <= -self.stop_loss_threshold:
            should_exit = True
            exit_reason = "stop_loss"
        elif position_type == 'short_spread' and z_score >= self.stop_loss_threshold:
            should_exit = True
            exit_reason = "stop_loss"
        
        # Time-based exit (if position is too old)
        position_age = (datetime.now() - position['entry_time']).total_seconds() / 60
        max_hold_time = self.pair_relationships[pair_key].half_life * 5  # 5x half-life
        
        if position_age > max_hold_time:
            should_exit = True
            exit_reason = "time_exit"
        
        if should_exit:
            # Generate exit signals
            if position_type == 'long_spread':
                # Close long spread: sell A, buy B
                signals.append({
                    'symbol': symbol_a,
                    'side': 'sell',
                    'order_type': 'market',
                    'quantity': position['quantity_a'],
                    'strength': 0.9,
                    'strategy_type': f'stat_arb_exit_{exit_reason}',
                    'pair_key': pair_key
                })
                signals.append({
                    'symbol': symbol_b,
                    'side': 'buy',
                    'order_type': 'market',
                    'quantity': position['quantity_b'],
                    'strength': 0.9,
                    'strategy_type': f'stat_arb_exit_{exit_reason}',
                    'pair_key': pair_key
                })
            else:  # short_spread
                # Close short spread: buy A, sell B
                signals.append({
                    'symbol': symbol_a,
                    'side': 'buy',
                    'order_type': 'market',
                    'quantity': position['quantity_a'],
                    'strength': 0.9,
                    'strategy_type': f'stat_arb_exit_{exit_reason}',
                    'pair_key': pair_key
                })
                signals.append({
                    'symbol': symbol_b,
                    'side': 'sell',
                    'order_type': 'market',
                    'quantity': position['quantity_b'],
                    'strength': 0.9,
                    'strategy_type': f'stat_arb_exit_{exit_reason}',
                    'pair_key': pair_key
                })
            
            # Remove position tracking
            del self.pair_positions[pair_key]
            self.active_pairs.discard(pair_key)
        
        return signals
    
    def _check_entry_signals(self, pair_key: Tuple[str, str], z_score: float, 
                           relationship: PairRelationship) -> List[Dict]:
        """Check for new entry opportunities"""
        signals = []
        symbol_a, symbol_b = pair_key
        
        # Check if we can take on more pairs
        if len(self.active_pairs) >= self.max_pairs:
            return signals
        
        # Check daily loss limit
        if self.current_daily_pnl < -self.max_daily_loss:
            return signals
        
        # Entry conditions
        if abs(z_score) >= self.entry_threshold:
            # Calculate position sizes
            position_value = min(self.max_position_size, self.initial_capital * 0.1)  # 10% max per pair
            
            price_a = self.price_history[symbol_a][-1]
            price_b = self.price_history[symbol_b][-1]
            
            # Calculate quantities based on hedge ratio
            quantity_a = position_value / (price_a + relationship.hedge_ratio * price_b)
            quantity_b = quantity_a * relationship.hedge_ratio
            
            if z_score >= self.entry_threshold:
                # Spread is too high - short the spread (sell B, buy A)
                position_type = 'short_spread'
                
                signals.append({
                    'symbol': symbol_a,
                    'side': 'buy',
                    'order_type': 'market',
                    'quantity': quantity_a,
                    'strength': min(1.0, abs(z_score) / 5.0),
                    'strategy_type': 'stat_arb_entry_short_spread',
                    'pair_key': pair_key
                })
                signals.append({
                    'symbol': symbol_b,
                    'side': 'sell',
                    'order_type': 'market',
                    'quantity': quantity_b,
                    'strength': min(1.0, abs(z_score) / 5.0),
                    'strategy_type': 'stat_arb_entry_short_spread',
                    'pair_key': pair_key
                })
                
            elif z_score <= -self.entry_threshold:
                # Spread is too low - long the spread (buy B, sell A)
                position_type = 'long_spread'
                
                signals.append({
                    'symbol': symbol_a,
                    'side': 'sell',
                    'order_type': 'market',
                    'quantity': quantity_a,
                    'strength': min(1.0, abs(z_score) / 5.0),
                    'strategy_type': 'stat_arb_entry_long_spread',
                    'pair_key': pair_key
                })
                signals.append({
                    'symbol': symbol_b,
                    'side': 'buy',
                    'order_type': 'market',
                    'quantity': quantity_b,
                    'strength': min(1.0, abs(z_score) / 5.0),
                    'strategy_type': 'stat_arb_entry_long_spread',
                    'pair_key': pair_key
                })
            
            # Track the new position
            if signals:
                self.pair_positions[pair_key] = {
                    'type': position_type,
                    'entry_z_score': z_score,
                    'entry_time': datetime.now(),
                    'quantity_a': quantity_a,
                    'quantity_b': quantity_b,
                    'entry_price_a': price_a,
                    'entry_price_b': price_b
                }
                self.active_pairs.add(pair_key)
        
        return signals
    
    async def update_price(self, symbol: str, price: float):
        """Update price history for symbol"""
        self.price_history[symbol].append(price)
        
        # Keep only recent history
        max_history = 500
        if len(self.price_history[symbol]) > max_history:
            self.price_history[symbol] = self.price_history[symbol][-max_history:]
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate strategy performance metrics"""
        if len(self.daily_returns) < 2:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'total_trades': self.trades_executed
            }
        
        returns = np.array(self.daily_returns)
        
        total_return = (1 + returns).prod() - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Calculate maximum drawdown
        cumulative_returns = (1 + returns).cumprod()
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / peak
        max_drawdown = np.min(drawdown)
        
        total_closed_trades = self.win_count + self.loss_count
        win_rate = self.win_count / total_closed_trades if total_closed_trades > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'win_rate': win_rate,
            'volatility': np.std(returns) * np.sqrt(252),
            'total_trades': self.trades_executed,
            'active_pairs': len(self.active_pairs),
            'discovered_pairs': len(self.pair_relationships),
            'avg_daily_return': np.mean(returns),
            'total_pnl': self.total_pnl
        }
    
    async def on_trade_executed(self, trade_info: Dict):
        """Handle trade execution callback"""
        self.trades_executed += 1
        
        # Update PnL tracking
        if 'pnl' in trade_info:
            self.total_pnl += trade_info['pnl']
            self.current_daily_pnl += trade_info['pnl']
            
            if trade_info['pnl'] > 0:
                self.win_count += 1
            else:
                self.loss_count += 1
        
        # Log trade
        self.logger.info(f"Statistical arbitrage trade executed: {trade_info}")

# Example usage
async def test_statistical_arbitrage():
    """Test the statistical arbitrage strategy"""
    symbols = ['BONK', 'WIF', 'POPCAT', 'FARTCOIN']
    strategy = StatisticalArbitrageStrategy(symbols)
    
    # Generate correlated price series for testing
    np.random.seed(42)
    base_prices = {'BONK': 0.000035, 'WIF': 2.20, 'POPCAT': 1.15, 'FARTCOIN': 1.28}
    
    # Create correlated random walks
    for i in range(200):
        common_factor = np.random.normal(0, 0.01)
        
        for symbol in symbols:
            idiosyncratic = np.random.normal(0, 0.005)
            price_change = common_factor + idiosyncratic
            
            if i == 0:
                new_price = base_prices[symbol]
            else:
                last_price = strategy.price_history[symbol][-1]
                new_price = last_price * (1 + price_change)
            
            await strategy.update_price(symbol, new_price)
    
    # Initialize and generate signals
    await strategy._discover_pairs()
    
    for i in range(50):
        signals = await strategy.generate_signals()
        if signals:
            print(f"Generated {len(signals)} signals")
        
        # Simulate more price updates
        common_factor = np.random.normal(0, 0.01)
        for symbol in symbols:
            idiosyncratic = np.random.normal(0, 0.005)
            price_change = common_factor + idiosyncratic
            last_price = strategy.price_history[symbol][-1]
            new_price = last_price * (1 + price_change)
            await strategy.update_price(symbol, new_price)
    
    # Print performance
    metrics = strategy.calculate_performance_metrics()
    print(f"Strategy Performance: {metrics}")

if __name__ == "__main__":
    asyncio.run(test_statistical_arbitrage())
