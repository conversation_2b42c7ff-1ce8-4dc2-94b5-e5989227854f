"""
Comprehensive Test Script for Enhanced Quantitative Trading System
Demonstrates full system functionality with output generation
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Import enhanced system components
from output_manager import OutputManager
from gmgn_integration import GMGNIntegration
from enhanced_momentum_strategy import EnhancedMomentumStrategy
from enhanced_mean_reversion_strategy import EnhancedMeanReversionStrategy
from data_analysis import DataAnalyzer

def create_test_data():
    """Create realistic test data for demonstration"""
    print("Creating realistic test data for demonstration...")
    
    # Create date range
    start_date = datetime(2025, 7, 16)
    dates = [start_date + timedelta(hours=i*0.5) for i in range(200)]
    
    # Real Solana tokens with realistic patterns
    tokens = {
        'BONK': {
            'base_price': 0.000035,
            'volatility': 0.08,
            'trend': 0.0002
        },
        'WIF': {
            'base_price': 2.20,
            'volatility': 0.06,
            'trend': 0.0001
        },
        'POPCAT': {
            'base_price': 1.15,
            'volatility': 0.12,
            'trend': 0.0003
        },
        'FARTCOIN': {
            'base_price': 1.28,
            'volatility': 0.10,
            'trend': -0.0001
        }
    }
    
    price_data = {}
    np.random.seed(42)  # For reproducible results
    
    for token_name, config in tokens.items():
        prices = []
        current_price = config['base_price']
        
        for i, date in enumerate(dates):
            # Realistic price movement
            trend_component = config['trend'] * i
            random_component = np.random.normal(0, config['volatility'])
            
            # Occasional large moves
            if np.random.random() < 0.02:
                random_component += np.random.choice([-1, 1]) * config['volatility'] * 3
            
            current_price = current_price * (1 + trend_component + random_component)
            current_price = max(current_price, config['base_price'] * 0.1)
            
            # Create realistic trading data
            volume = np.random.lognormal(8, 1)
            
            prices.append({
                'datetime': date,
                'price': current_price,
                'usd_value': volume,
                'total_profit': np.random.normal(0, current_price * 0.05),
                'liquidity': np.random.uniform(10000, 100000)
            })
        
        price_data[token_name] = pd.DataFrame(prices)
    
    return price_data

def test_gmgn_integration():
    """Test GMGN.ai integration functionality"""
    print("\n" + "="*60)
    print("TESTING GMGN.AI INTEGRATION")
    print("="*60)
    
    gmgn = GMGNIntegration()
    
    # Test wallet analysis
    print("Testing wallet analysis...")
    wallet_data = gmgn.get_wallet_analysis()
    if wallet_data:
        print(f"✅ Wallet Analysis Success:")
        print(f"   Smart Money Score: {wallet_data.get('smart_money_score', 0)}/10")
        print(f"   30-Day Win Rate: {wallet_data.get('pnl_30d', {}).get('win_rate', 0)*100:.1f}%")
        print(f"   Data Source: {wallet_data.get('data_source', 'unknown')}")
    
    # Test trending tokens
    print("\nTesting trending tokens...")
    trending = gmgn.get_trending_tokens(5)
    print(f"✅ Trending Tokens ({len(trending)} fetched):")
    for i, token in enumerate(trending[:3], 1):
        print(f"   {i}. {token['symbol']}: ${token['price']:.6f} ({token['price_change_24h']*100:+.1f}%)")
    
    # Test smart money signals
    print("\nTesting smart money signals...")
    signals = gmgn.get_smart_money_signals()
    print(f"✅ Smart Money Signals: {len(signals)} active signals")
    
    # Test market sentiment
    print("\nTesting market sentiment...")
    sentiment = gmgn.get_market_sentiment()
    print(f"✅ Market Sentiment: {sentiment.get('sentiment', 'unknown')} "
          f"({sentiment.get('confidence', 0)*100:.1f}% confidence)")
    
    return {
        'wallet_data': wallet_data,
        'trending': trending,
        'signals': signals,
        'sentiment': sentiment
    }

def test_output_manager():
    """Test output manager functionality"""
    print("\n" + "="*60)
    print("TESTING OUTPUT MANAGER")
    print("="*60)
    
    # Initialize output manager
    output_manager = OutputManager("test_trading_results")
    
    # Test trade logging
    test_trade = {
        'symbol': 'BONK',
        'action': 'BUY',
        'price': 0.000035,
        'shares': 1000000,
        'value': 35,
        'profit_loss': 0,
        'return_pct': 0,
        'gmgn_score': 8.5,
        'smart_money_signals': 2,
        'market_sentiment': 'bullish'
    }
    
    output_manager.log_trade(test_trade, 'Test Strategy')
    print("✅ Trade logging test completed")
    
    # Test performance logging
    test_metrics = {
        'total_return': 0.15,
        'sharpe_ratio': 1.2,
        'win_rate': 0.65,
        'total_trades': 10,
        'final_capital': 115000
    }
    
    output_manager.log_performance_snapshot('Test Strategy', test_metrics)
    print("✅ Performance logging test completed")
    
    # Test GMGN data logging
    test_gmgn_data = {
        'smart_money_score': 8.5,
        'win_rate': 0.68,
        'sentiment': 'bullish'
    }
    
    output_manager.log_gmgn_data(test_gmgn_data, 'test_data')
    print("✅ GMGN data logging test completed")
    
    # Test file generation
    output_manager.save_trade_records('Test Strategy')
    output_manager.save_performance_metrics('Test Strategy', test_metrics)
    output_manager.save_gmgn_data()
    
    print(f"✅ Output files generated in: {output_manager.base_dir}")
    
    return output_manager

def test_enhanced_strategies():
    """Test enhanced trading strategies with output generation"""
    print("\n" + "="*60)
    print("TESTING ENHANCED TRADING STRATEGIES")
    print("="*60)
    
    # Create test data
    price_data = create_test_data()
    
    # Initialize output manager
    output_manager = OutputManager("strategy_test_results")
    
    # Test Enhanced Momentum Strategy
    print("\nTesting Enhanced Momentum Strategy...")
    momentum_strategy = EnhancedMomentumStrategy(
        initial_capital=100000,
        max_positions=5,
        position_size=0.15,
        output_manager=output_manager
    )
    
    momentum_results = momentum_strategy.run_backtest(price_data)
    momentum_strategy.print_performance_report()
    
    # Test Enhanced Mean Reversion Strategy
    print("\nTesting Enhanced Mean Reversion Strategy...")
    mean_reversion_strategy = EnhancedMeanReversionStrategy(
        initial_capital=100000,
        max_positions=8,
        position_size=0.10,
        output_manager=output_manager
    )
    
    mean_reversion_results = mean_reversion_strategy.run_backtest(price_data)
    mean_reversion_strategy.print_performance_report()
    
    # Generate final summary
    results_data = {
        'enhanced_momentum': momentum_results,
        'enhanced_mean_reversion': mean_reversion_results,
        'test_data': True
    }
    
    summary = output_manager.finalize_output(results_data)
    
    print(f"\n✅ Strategy testing completed")
    print(f"📁 Results saved to: {output_manager.base_dir}")
    
    return {
        'momentum_results': momentum_results,
        'mean_reversion_results': mean_reversion_results,
        'output_manager': output_manager,
        'summary': summary
    }

def test_real_data_integration():
    """Test integration with real CSV data if available"""
    print("\n" + "="*60)
    print("TESTING REAL DATA INTEGRATION")
    print("="*60)
    
    csv_file = "Quant test.csv"
    
    if os.path.exists(csv_file):
        print(f"✅ Found real data file: {csv_file}")
        
        try:
            # Test data analysis
            analyzer = DataAnalyzer(csv_file)
            analyzer.load_data()
            analyzer.parse_trading_history()
            price_data = analyzer.create_price_series()
            
            print(f"✅ Real data analysis completed:")
            print(f"   Tokens with price data: {len(price_data)}")
            
            # Test with small subset for demonstration
            if len(price_data) > 0:
                sample_tokens = dict(list(price_data.items())[:3])
                print(f"   Testing with {len(sample_tokens)} sample tokens")
                
                # Quick test with real data
                output_manager = OutputManager("real_data_test")
                
                momentum_strategy = EnhancedMomentumStrategy(
                    initial_capital=50000,  # Smaller capital for test
                    max_positions=3,
                    position_size=0.20,
                    output_manager=output_manager
                )
                
                results = momentum_strategy.run_backtest(sample_tokens)
                print(f"✅ Real data test completed - Return: {results.get('total_return', 0)*100:.2f}%")
                
                return True
            
        except Exception as e:
            print(f"⚠️  Real data test error: {e}")
            return False
    else:
        print(f"⚠️  Real data file not found: {csv_file}")
        print("   Using synthetic data for testing")
        return False

def main():
    """Main test execution function"""
    print("="*80)
    print("ENHANCED QUANTITATIVE TRADING SYSTEM - COMPREHENSIVE TEST")
    print("="*80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = {}
    
    try:
        # Test 1: GMGN.ai Integration
        gmgn_results = test_gmgn_integration()
        test_results['gmgn_integration'] = gmgn_results
        
        # Test 2: Output Manager
        output_manager = test_output_manager()
        test_results['output_manager'] = output_manager
        
        # Test 3: Enhanced Strategies
        strategy_results = test_enhanced_strategies()
        test_results['strategies'] = strategy_results
        
        # Test 4: Real Data Integration (if available)
        real_data_success = test_real_data_integration()
        test_results['real_data'] = real_data_success
        
        # Final Summary
        print("\n" + "="*80)
        print("TEST EXECUTION SUMMARY")
        print("="*80)
        
        print("✅ GMGN.ai Integration: PASSED")
        print("✅ Output Manager: PASSED")
        print("✅ Enhanced Strategies: PASSED")
        print(f"{'✅' if real_data_success else '⚠️ '} Real Data Integration: {'PASSED' if real_data_success else 'SKIPPED'}")
        
        print(f"\n🎯 KEY ACHIEVEMENTS:")
        print(f"   - Dual data source integration (CSV + GMGN.ai)")
        print(f"   - Enhanced trading strategies with smart money tracking")
        print(f"   - Comprehensive output file generation")
        print(f"   - Real-time logging and performance tracking")
        print(f"   - Structured results organization")
        
        print(f"\n📁 OUTPUT LOCATIONS:")
        if 'strategies' in test_results:
            output_dir = test_results['strategies']['output_manager'].base_dir
            print(f"   Strategy Test Results: {output_dir}")
        
        print(f"   Test Trading Results: test_trading_results/")
        print(f"   Real Data Test: real_data_test/ (if applicable)")
        
        print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        return test_results
        
    except Exception as e:
        print(f"\n❌ Test execution error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    try:
        results = main()
        if results:
            print(f"\n🎉 Enhanced system testing completed successfully!")
            print(f"📋 All components verified and output files generated.")
        else:
            print(f"\n❌ Testing encountered errors.")
    except KeyboardInterrupt:
        print(f"\n⏹️  Testing interrupted by user.")
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
