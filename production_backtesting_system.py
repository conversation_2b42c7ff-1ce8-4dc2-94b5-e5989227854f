"""
Production Backtesting System
Comprehensive backtesting for institutional-grade trading strategies
Target: Demonstrate >60% win rate, Sharpe >2.0, <5% max drawdown
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
import warnings
warnings.filterwarnings('ignore')

from production_trading_engine import ProductionTradingEngine
from market_making_strategy import MarketMakingStrategy
from statistical_arbitrage_strategy import StatisticalArbitrageStrategy
from high_frequency_momentum_strategy import HighFrequencyMomentumStrategy
from advanced_risk_management import AdvancedRiskManager
from output_manager import OutputManager

class ProductionBacktestingSystem:
    """Comprehensive backtesting system for production strategies"""
    
    def __init__(self, initial_capital: float = 1000000):
        self.initial_capital = initial_capital
        self.output_manager = OutputManager("production_backtest_results")
        
        # Strategy allocations
        self.strategy_allocations = {
            'market_making': 0.4,      # 40% to market making
            'statistical_arbitrage': 0.3,  # 30% to stat arb
            'hf_momentum': 0.3         # 30% to HF momentum
        }
        
        # Performance tracking
        self.strategy_performance = {}
        self.combined_performance = {}
        
        # Logging
        self.logger = logging.getLogger('ProductionBacktesting')
        
    def generate_realistic_market_data(self, symbols: List[str], 
                                     num_days: int = 30) -> Dict[str, pd.DataFrame]:
        """Generate realistic market data for backtesting"""
        
        self.logger.info(f"Generating {num_days} days of realistic market data for {len(symbols)} symbols")
        
        # Base parameters for each symbol
        symbol_params = {
            'BONK': {'base_price': 0.000035, 'volatility': 0.08, 'trend': 0.0001},
            'WIF': {'base_price': 2.20, 'volatility': 0.06, 'trend': 0.00005},
            'POPCAT': {'base_price': 1.15, 'volatility': 0.12, 'trend': 0.0002},
            'FARTCOIN': {'base_price': 1.28, 'volatility': 0.10, 'trend': -0.00005},
            'SOL': {'base_price': 150.0, 'volatility': 0.05, 'trend': 0.0001}
        }
        
        # Generate correlated market data
        np.random.seed(42)  # For reproducible results
        
        # Time parameters
        minutes_per_day = 1440
        total_minutes = num_days * minutes_per_day
        timestamps = [datetime.now() - timedelta(minutes=total_minutes-i) for i in range(total_minutes)]
        
        market_data = {}
        
        # Generate common market factor
        market_factor = np.random.normal(0, 0.002, total_minutes)
        
        for symbol in symbols:
            if symbol not in symbol_params:
                continue
                
            params = symbol_params[symbol]
            
            # Generate price series with realistic features
            prices = []
            volumes = []
            current_price = params['base_price']
            
            for i in range(total_minutes):
                # Market microstructure effects
                if i % 60 == 0:  # Hourly volatility clustering
                    vol_multiplier = np.random.lognormal(0, 0.3)
                else:
                    vol_multiplier = 1.0
                
                # Price movement components
                trend_component = params['trend']
                market_component = market_factor[i] * 0.7  # 70% correlation with market
                idiosyncratic_component = np.random.normal(0, params['volatility'] * vol_multiplier)
                
                # Occasional large moves (fat tails)
                if np.random.random() < 0.001:  # 0.1% chance
                    shock = np.random.choice([-1, 1]) * np.random.exponential(0.05)
                    idiosyncratic_component += shock
                
                # Update price
                total_return = trend_component + market_component + idiosyncratic_component
                current_price *= (1 + total_return)
                current_price = max(current_price, params['base_price'] * 0.1)  # Floor price
                
                prices.append(current_price)
                
                # Generate realistic volume
                base_volume = 10000
                volume_factor = np.random.lognormal(0, 0.5)
                volatility_volume_effect = abs(total_return) * 50000  # Higher volume during volatile periods
                volume = base_volume * volume_factor + volatility_volume_effect
                volumes.append(volume)
            
            # Create DataFrame
            df = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': volumes
            })
            
            # Add bid-ask spread
            df['spread_bps'] = np.random.uniform(5, 20, len(df))  # 5-20 basis points
            df['bid'] = df['price'] * (1 - df['spread_bps'] / 20000)
            df['ask'] = df['price'] * (1 + df['spread_bps'] / 20000)
            
            # Add liquidity metrics
            df['liquidity'] = df['volume'] * df['price'] * np.random.uniform(0.8, 1.2, len(df))
            
            market_data[symbol] = df
        
        return market_data
    
    async def run_strategy_backtest(self, strategy_name: str, strategy_instance,
                                  market_data: Dict[str, pd.DataFrame]) -> Dict:
        """Run backtest for a specific strategy"""
        
        self.logger.info(f"Running backtest for {strategy_name}")
        
        # Initialize performance tracking
        trades = []
        daily_pnl = []
        portfolio_values = []
        
        # Get strategy allocation
        allocated_capital = self.initial_capital * self.strategy_allocations[strategy_name]
        current_capital = allocated_capital
        
        # Simulate trading over the data period
        symbols = list(market_data.keys())
        total_minutes = len(market_data[symbols[0]])
        
        for minute in range(0, total_minutes, 10):  # Process every 10 minutes
            try:
                # Update market data for strategy
                for symbol in symbols:
                    if minute < len(market_data[symbol]):
                        row = market_data[symbol].iloc[minute]
                        
                        if strategy_name == 'market_making':
                            await strategy_instance.update_market_data(
                                symbol, row['bid'], row['ask'], row['price'], row['volume']
                            )
                        elif strategy_name == 'statistical_arbitrage':
                            await strategy_instance.update_price(symbol, row['price'])
                        elif strategy_name == 'hf_momentum':
                            # For HF strategy, we'd process tick by tick
                            pass
                
                # Generate signals
                signals = await strategy_instance.generate_signals()
                
                # Process signals (simplified execution)
                for signal in signals:
                    trade = await self._execute_backtest_signal(signal, market_data, minute)
                    if trade:
                        trades.append(trade)
                        current_capital += trade.get('pnl', 0)
                
                # Track portfolio value
                portfolio_values.append(current_capital)
                
                # Calculate daily PnL
                if minute % 1440 == 0 and minute > 0:  # End of day
                    daily_return = (current_capital - allocated_capital) / allocated_capital
                    daily_pnl.append(daily_return)
                
            except Exception as e:
                self.logger.error(f"Error in {strategy_name} backtest at minute {minute}: {e}")
        
        # Calculate performance metrics
        performance = self._calculate_strategy_performance(
            trades, daily_pnl, portfolio_values, allocated_capital, current_capital
        )
        
        # Log strategy performance
        self.output_manager.log_performance_snapshot(strategy_name, performance)
        
        return performance
    
    async def _execute_backtest_signal(self, signal: Dict, market_data: Dict[str, pd.DataFrame],
                                     current_minute: int) -> Optional[Dict]:
        """Execute signal in backtest environment"""
        
        symbol = signal['symbol']
        if symbol not in market_data or current_minute >= len(market_data[symbol]):
            return None
        
        # Get current market data
        current_data = market_data[symbol].iloc[current_minute]
        
        # Simulate execution with realistic slippage
        execution_price = current_data['price']
        slippage_bps = np.random.uniform(1, 5)  # 1-5 basis points slippage
        
        if signal['side'] == 'buy':
            execution_price *= (1 + slippage_bps / 10000)
        else:
            execution_price *= (1 - slippage_bps / 10000)
        
        # Calculate trade value
        quantity = signal['quantity']
        trade_value = quantity * execution_price
        
        # Simulate holding period and exit
        hold_minutes = signal.get('expected_hold_time', 30)  # Default 30 minutes
        exit_minute = min(current_minute + hold_minutes, len(market_data[symbol]) - 1)
        exit_data = market_data[symbol].iloc[exit_minute]
        
        # Calculate PnL
        if signal['side'] == 'buy':
            pnl = quantity * (exit_data['price'] - execution_price)
        else:
            pnl = quantity * (execution_price - exit_data['price'])
        
        # Apply transaction costs
        transaction_cost = trade_value * 0.0005  # 5 basis points
        pnl -= transaction_cost
        
        return {
            'timestamp': current_data['timestamp'],
            'symbol': symbol,
            'side': signal['side'],
            'quantity': quantity,
            'entry_price': execution_price,
            'exit_price': exit_data['price'],
            'pnl': pnl,
            'return_pct': pnl / trade_value,
            'hold_minutes': hold_minutes,
            'strategy_type': signal.get('strategy_type', 'unknown')
        }
    
    def _calculate_strategy_performance(self, trades: List[Dict], daily_pnl: List[float],
                                      portfolio_values: List[float], initial_capital: float,
                                      final_capital: float) -> Dict:
        """Calculate comprehensive strategy performance metrics"""
        
        if not trades:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'total_trades': 0
            }
        
        # Basic metrics
        total_return = (final_capital - initial_capital) / initial_capital
        total_trades = len(trades)
        
        # Win rate
        winning_trades = [t for t in trades if t['pnl'] > 0]
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
        
        # Sharpe ratio
        if daily_pnl and len(daily_pnl) > 1:
            daily_returns = np.array(daily_pnl)
            sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252) if np.std(daily_returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Maximum drawdown
        if portfolio_values and len(portfolio_values) > 1:
            values = np.array(portfolio_values)
            peak = np.maximum.accumulate(values)
            drawdown = (values - peak) / peak
            max_drawdown = abs(np.min(drawdown))
        else:
            max_drawdown = 0
        
        # Additional metrics
        avg_trade_pnl = np.mean([t['pnl'] for t in trades]) if trades else 0
        avg_trade_return = np.mean([t['return_pct'] for t in trades]) if trades else 0
        volatility = np.std(daily_pnl) * np.sqrt(252) if daily_pnl else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'avg_trade_pnl': avg_trade_pnl,
            'avg_trade_return': avg_trade_return,
            'volatility': volatility,
            'final_capital': final_capital,
            'profit_factor': sum(t['pnl'] for t in winning_trades) / abs(sum(t['pnl'] for t in trades if t['pnl'] < 0)) if any(t['pnl'] < 0 for t in trades) else float('inf')
        }
    
    async def run_comprehensive_backtest(self) -> Dict:
        """Run comprehensive backtest of all production strategies"""
        
        self.logger.info("Starting comprehensive production strategy backtest")
        
        # Generate market data
        symbols = ['BONK', 'WIF', 'POPCAT', 'FARTCOIN', 'SOL']
        market_data = self.generate_realistic_market_data(symbols, num_days=30)
        
        # Initialize strategies
        strategies = {
            'market_making': MarketMakingStrategy(symbols[:3], self.initial_capital * 0.4),
            'statistical_arbitrage': StatisticalArbitrageStrategy(symbols, self.initial_capital * 0.3),
            'hf_momentum': HighFrequencyMomentumStrategy(symbols[:2], self.initial_capital * 0.3)
        }
        
        # Run individual strategy backtests
        strategy_results = {}
        
        for strategy_name, strategy_instance in strategies.items():
            try:
                performance = await self.run_strategy_backtest(strategy_name, strategy_instance, market_data)
                strategy_results[strategy_name] = performance
                
                self.logger.info(f"{strategy_name} Performance:")
                self.logger.info(f"  Total Return: {performance['total_return']*100:.2f}%")
                self.logger.info(f"  Sharpe Ratio: {performance['sharpe_ratio']:.3f}")
                self.logger.info(f"  Max Drawdown: {performance['max_drawdown']*100:.2f}%")
                self.logger.info(f"  Win Rate: {performance['win_rate']*100:.1f}%")
                self.logger.info(f"  Total Trades: {performance['total_trades']}")
                
            except Exception as e:
                self.logger.error(f"Error backtesting {strategy_name}: {e}")
                strategy_results[strategy_name] = {}
        
        # Calculate combined portfolio performance
        combined_performance = self._calculate_combined_performance(strategy_results)
        
        # Generate comprehensive report
        report = {
            'backtest_summary': {
                'start_date': datetime.now() - timedelta(days=30),
                'end_date': datetime.now(),
                'initial_capital': self.initial_capital,
                'symbols_traded': symbols,
                'strategies_tested': list(strategies.keys())
            },
            'individual_strategies': strategy_results,
            'combined_portfolio': combined_performance,
            'target_metrics_achieved': self._check_target_metrics(combined_performance)
        }
        
        # Save results
        self.output_manager.finalize_output(report)
        
        return report
    
    def _calculate_combined_performance(self, strategy_results: Dict) -> Dict:
        """Calculate combined portfolio performance"""
        
        total_allocated = sum(self.initial_capital * allocation for allocation in self.strategy_allocations.values())
        total_final = 0
        
        weighted_metrics = {
            'total_return': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'win_rate': 0,
            'total_trades': 0
        }
        
        for strategy_name, allocation in self.strategy_allocations.items():
            if strategy_name in strategy_results and strategy_results[strategy_name]:
                performance = strategy_results[strategy_name]
                weight = allocation
                
                # Weight metrics by allocation
                weighted_metrics['total_return'] += performance.get('total_return', 0) * weight
                weighted_metrics['sharpe_ratio'] += performance.get('sharpe_ratio', 0) * weight
                weighted_metrics['max_drawdown'] = max(weighted_metrics['max_drawdown'], performance.get('max_drawdown', 0))
                weighted_metrics['win_rate'] += performance.get('win_rate', 0) * weight
                weighted_metrics['total_trades'] += performance.get('total_trades', 0)
                
                total_final += performance.get('final_capital', self.initial_capital * allocation)
        
        weighted_metrics['final_capital'] = total_final
        
        return weighted_metrics
    
    def _check_target_metrics(self, performance: Dict) -> Dict:
        """Check if target performance metrics are achieved"""
        
        targets = {
            'win_rate': 0.60,      # >60% win rate
            'sharpe_ratio': 2.0,   # >2.0 Sharpe ratio
            'max_drawdown': 0.05   # <5% max drawdown
        }
        
        achieved = {}
        
        for metric, target in targets.items():
            current_value = performance.get(metric, 0)
            
            if metric == 'max_drawdown':
                achieved[metric] = current_value < target
            else:
                achieved[metric] = current_value > target
            
            achieved[f'{metric}_value'] = current_value
            achieved[f'{metric}_target'] = target
        
        achieved['all_targets_met'] = all(achieved[metric] for metric in targets.keys())
        
        return achieved
    
    def print_backtest_summary(self, results: Dict):
        """Print comprehensive backtest summary"""
        
        print("\n" + "="*80)
        print("PRODUCTION TRADING SYSTEM - BACKTEST RESULTS")
        print("="*80)
        
        # Individual strategy performance
        print("\nINDIVIDUAL STRATEGY PERFORMANCE:")
        print("-" * 50)
        
        for strategy_name, performance in results['individual_strategies'].items():
            if performance:
                print(f"\n{strategy_name.upper().replace('_', ' ')}:")
                print(f"  Total Return: {performance.get('total_return', 0)*100:.2f}%")
                print(f"  Sharpe Ratio: {performance.get('sharpe_ratio', 0):.3f}")
                print(f"  Max Drawdown: {performance.get('max_drawdown', 0)*100:.2f}%")
                print(f"  Win Rate: {performance.get('win_rate', 0)*100:.1f}%")
                print(f"  Total Trades: {performance.get('total_trades', 0)}")
        
        # Combined portfolio performance
        combined = results['combined_portfolio']
        print(f"\nCOMBINED PORTFOLIO PERFORMANCE:")
        print("-" * 50)
        print(f"Total Return: {combined.get('total_return', 0)*100:.2f}%")
        print(f"Sharpe Ratio: {combined.get('sharpe_ratio', 0):.3f}")
        print(f"Max Drawdown: {combined.get('max_drawdown', 0)*100:.2f}%")
        print(f"Win Rate: {combined.get('win_rate', 0)*100:.1f}%")
        print(f"Total Trades: {combined.get('total_trades', 0)}")
        
        # Target achievement
        targets = results['target_metrics_achieved']
        print(f"\nTARGET METRICS ACHIEVEMENT:")
        print("-" * 50)
        print(f"Win Rate Target (>60%): {'✅' if targets.get('win_rate', False) else '❌'} "
              f"({targets.get('win_rate_value', 0)*100:.1f}%)")
        print(f"Sharpe Ratio Target (>2.0): {'✅' if targets.get('sharpe_ratio', False) else '❌'} "
              f"({targets.get('sharpe_ratio_value', 0):.3f})")
        print(f"Max Drawdown Target (<5%): {'✅' if targets.get('max_drawdown', False) else '❌'} "
              f"({targets.get('max_drawdown_value', 0)*100:.2f}%)")
        
        overall_success = targets.get('all_targets_met', False)
        print(f"\nOVERALL TARGET ACHIEVEMENT: {'✅ SUCCESS' if overall_success else '❌ NEEDS IMPROVEMENT'}")

# Example usage
async def main():
    """Run production backtesting system"""
    
    backtesting_system = ProductionBacktestingSystem(initial_capital=1000000)
    
    # Run comprehensive backtest
    results = await backtesting_system.run_comprehensive_backtest()
    
    # Print results
    backtesting_system.print_backtest_summary(results)
    
    return results

if __name__ == "__main__":
    results = asyncio.run(main())
