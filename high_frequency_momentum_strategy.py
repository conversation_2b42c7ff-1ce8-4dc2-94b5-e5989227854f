"""
High-Frequency Momentum Strategy
Ultra-short-term momentum capture with microsecond execution
Target: >70% win rate, Sharpe ratio >3.0, <2% max drawdown
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Deque
from dataclasses import dataclass
from collections import deque
import logging
import time

from production_trading_engine import OrderSide, OrderType, ProductionTradingEngine

@dataclass
class TickData:
    """High-frequency tick data structure"""
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    bid: float
    ask: float
    bid_size: float
    ask_size: float
    
@dataclass
class MomentumSignal:
    """Momentum signal with confidence and timing"""
    symbol: str
    direction: str  # 'up' or 'down'
    strength: float  # 0-1
    confidence: float  # 0-1
    expected_duration_ms: int
    timestamp: datetime

class MicrostructureAnalyzer:
    """Advanced microstructure analysis for HF momentum"""
    
    def __init__(self, tick_window: int = 100):
        self.tick_window = tick_window
        self.volume_imbalance_threshold = 0.3
        self.price_acceleration_threshold = 0.0001
        
    def analyze_order_flow(self, ticks: Deque[TickData]) -> Dict:
        """Analyze order flow for momentum signals"""
        if len(ticks) < 10:
            return {}
        
        recent_ticks = list(ticks)[-10:]
        
        # Calculate volume-weighted average price (VWAP)
        total_volume = sum(tick.volume for tick in recent_ticks)
        if total_volume == 0:
            return {}
        
        vwap = sum(tick.price * tick.volume for tick in recent_ticks) / total_volume
        
        # Calculate order flow imbalance
        buy_volume = sum(tick.volume for tick in recent_ticks if tick.price >= vwap)
        sell_volume = sum(tick.volume for tick in recent_ticks if tick.price < vwap)
        
        total_flow_volume = buy_volume + sell_volume
        if total_flow_volume == 0:
            return {}
        
        order_flow_imbalance = (buy_volume - sell_volume) / total_flow_volume
        
        # Calculate price acceleration
        if len(recent_ticks) >= 5:
            prices = [tick.price for tick in recent_ticks[-5:]]
            price_changes = np.diff(prices)
            price_acceleration = np.diff(price_changes).mean() if len(price_changes) > 1 else 0
        else:
            price_acceleration = 0
        
        # Calculate bid-ask spread dynamics
        spreads = [(tick.ask - tick.bid) / tick.price for tick in recent_ticks if tick.ask > tick.bid]
        avg_spread = np.mean(spreads) if spreads else 0
        spread_trend = np.polyfit(range(len(spreads)), spreads, 1)[0] if len(spreads) > 2 else 0
        
        return {
            'vwap': vwap,
            'order_flow_imbalance': order_flow_imbalance,
            'price_acceleration': price_acceleration,
            'avg_spread': avg_spread,
            'spread_trend': spread_trend,
            'buy_volume': buy_volume,
            'sell_volume': sell_volume
        }
    
    def detect_momentum_regime(self, ticks: Deque[TickData]) -> str:
        """Detect current momentum regime"""
        if len(ticks) < 20:
            return 'unknown'
        
        recent_ticks = list(ticks)[-20:]
        prices = [tick.price for tick in recent_ticks]
        volumes = [tick.volume for tick in recent_ticks]
        
        # Calculate short-term momentum
        short_momentum = (prices[-1] - prices[-5]) / prices[-5] if len(prices) >= 5 else 0
        
        # Calculate volume trend
        volume_trend = np.polyfit(range(len(volumes)), volumes, 1)[0] if len(volumes) > 2 else 0
        
        # Calculate volatility
        returns = np.diff(np.log(prices))
        volatility = np.std(returns) if len(returns) > 1 else 0
        
        # Regime classification
        if abs(short_momentum) > 0.001 and volume_trend > 0 and volatility < 0.01:
            return 'trending'
        elif volatility > 0.02:
            return 'volatile'
        elif abs(short_momentum) < 0.0005:
            return 'ranging'
        else:
            return 'transitional'

class HighFrequencyMomentumStrategy:
    """Ultra-high-frequency momentum strategy"""
    
    def __init__(self, symbols: List[str], initial_capital: float = 1000000):
        self.symbols = symbols
        self.initial_capital = initial_capital
        self.strategy_id = "hf_momentum_v1"
        
        # HF Strategy parameters
        self.momentum_threshold = 0.0005  # 5 basis points
        self.min_volume_threshold = 1000  # Minimum volume for signal
        self.max_hold_time_ms = 5000  # Maximum 5 seconds hold time
        self.position_size_bps = 50  # 50 basis points position size
        self.max_positions_per_symbol = 3
        
        # Execution parameters
        self.target_latency_ms = 0.5  # Target sub-millisecond execution
        self.slippage_tolerance_bps = 2  # 2 basis points slippage tolerance
        
        # Data storage
        self.tick_data: Dict[str, Deque[TickData]] = {
            symbol: deque(maxlen=1000) for symbol in symbols
        }
        
        # Analysis components
        self.microstructure_analyzer = MicrostructureAnalyzer()
        
        # Position tracking
        self.active_positions: Dict[str, List[Dict]] = {symbol: [] for symbol in symbols}
        
        # Performance tracking
        self.trades_executed = 0
        self.total_pnl = 0.0
        self.win_count = 0
        self.loss_count = 0
        self.execution_times = []
        
        # Risk management
        self.max_daily_trades = 1000  # Limit to prevent overtrading
        self.daily_trade_count = 0
        self.max_daily_loss = initial_capital * 0.01  # 1% daily loss limit
        self.current_daily_pnl = 0.0
        
        # Logging
        self.logger = logging.getLogger(f'HFMomentum_{self.strategy_id}')
        
    async def initialize(self, trading_engine: ProductionTradingEngine):
        """Initialize strategy with trading engine"""
        self.trading_engine = trading_engine
        self.logger.info(f"High-Frequency Momentum Strategy initialized for {len(self.symbols)} symbols")
        
    async def process_tick(self, tick_data: TickData):
        """Process incoming tick data with ultra-low latency"""
        start_time = time.perf_counter()
        
        symbol = tick_data.symbol
        self.tick_data[symbol].append(tick_data)
        
        # Generate signals immediately
        signals = await self._generate_tick_signals(symbol)
        
        # Execute signals with minimal latency
        for signal in signals:
            await self._execute_hf_signal(signal)
        
        # Track execution time
        execution_time_ms = (time.perf_counter() - start_time) * 1000
        self.execution_times.append(execution_time_ms)
        
        # Keep only recent execution times
        if len(self.execution_times) > 1000:
            self.execution_times = self.execution_times[-1000:]
    
    async def _generate_tick_signals(self, symbol: str) -> List[MomentumSignal]:
        """Generate momentum signals from tick data"""
        ticks = self.tick_data[symbol]
        
        if len(ticks) < 10:
            return []
        
        # Check trading limits
        if (self.daily_trade_count >= self.max_daily_trades or
            self.current_daily_pnl < -self.max_daily_loss):
            return []
        
        # Analyze microstructure
        microstructure = self.microstructure_analyzer.analyze_order_flow(ticks)
        if not microstructure:
            return []
        
        # Detect momentum regime
        regime = self.microstructure_analyzer.detect_momentum_regime(ticks)
        
        signals = []
        
        # Generate signals based on multiple factors
        signal = self._analyze_momentum_factors(symbol, microstructure, regime)
        if signal:
            signals.append(signal)
        
        return signals
    
    def _analyze_momentum_factors(self, symbol: str, microstructure: Dict, regime: str) -> Optional[MomentumSignal]:
        """Analyze multiple momentum factors"""
        
        # Factor 1: Order flow imbalance
        flow_imbalance = microstructure.get('order_flow_imbalance', 0)
        flow_signal_strength = abs(flow_imbalance) if abs(flow_imbalance) > 0.2 else 0
        
        # Factor 2: Price acceleration
        price_acceleration = microstructure.get('price_acceleration', 0)
        acceleration_signal_strength = min(1.0, abs(price_acceleration) * 10000)
        
        # Factor 3: Volume surge
        buy_volume = microstructure.get('buy_volume', 0)
        sell_volume = microstructure.get('sell_volume', 0)
        total_volume = buy_volume + sell_volume
        
        volume_signal_strength = 0
        if total_volume > self.min_volume_threshold:
            volume_signal_strength = min(1.0, total_volume / (self.min_volume_threshold * 5))
        
        # Factor 4: Spread dynamics
        spread_trend = microstructure.get('spread_trend', 0)
        spread_signal_strength = min(1.0, abs(spread_trend) * 1000)
        
        # Combine factors with regime-based weighting
        if regime == 'trending':
            weights = [0.4, 0.3, 0.2, 0.1]  # Emphasize flow and acceleration
        elif regime == 'volatile':
            weights = [0.3, 0.2, 0.4, 0.1]  # Emphasize volume
        elif regime == 'ranging':
            weights = [0.2, 0.1, 0.3, 0.4]  # Emphasize spread dynamics
        else:
            weights = [0.25, 0.25, 0.25, 0.25]  # Equal weights
        
        combined_strength = (
            flow_signal_strength * weights[0] +
            acceleration_signal_strength * weights[1] +
            volume_signal_strength * weights[2] +
            spread_signal_strength * weights[3]
        )
        
        # Determine direction
        direction_score = (
            np.sign(flow_imbalance) * weights[0] +
            np.sign(price_acceleration) * weights[1] +
            np.sign(buy_volume - sell_volume) * weights[2] +
            -np.sign(spread_trend) * weights[3]  # Tightening spread = positive momentum
        )
        
        # Generate signal if strength is sufficient
        if combined_strength > 0.3 and abs(direction_score) > 0.1:
            direction = 'up' if direction_score > 0 else 'down'
            
            # Calculate confidence based on factor agreement
            factor_agreement = sum([
                1 if np.sign(flow_imbalance) == np.sign(direction_score) else 0,
                1 if np.sign(price_acceleration) == np.sign(direction_score) else 0,
                1 if np.sign(buy_volume - sell_volume) == np.sign(direction_score) else 0,
                1 if -np.sign(spread_trend) == np.sign(direction_score) else 0
            ]) / 4
            
            # Expected duration based on regime and strength
            if regime == 'trending':
                expected_duration = int(2000 + combined_strength * 3000)  # 2-5 seconds
            elif regime == 'volatile':
                expected_duration = int(500 + combined_strength * 1500)   # 0.5-2 seconds
            else:
                expected_duration = int(1000 + combined_strength * 2000)  # 1-3 seconds
            
            return MomentumSignal(
                symbol=symbol,
                direction=direction,
                strength=combined_strength,
                confidence=factor_agreement,
                expected_duration_ms=min(expected_duration, self.max_hold_time_ms),
                timestamp=datetime.now()
            )
        
        return None
    
    async def _execute_hf_signal(self, signal: MomentumSignal):
        """Execute high-frequency signal with minimal latency"""
        symbol = signal.symbol
        
        # Check position limits
        active_positions = len(self.active_positions[symbol])
        if active_positions >= self.max_positions_per_symbol:
            return
        
        # Calculate position size
        current_price = self.tick_data[symbol][-1].price
        position_value = self.initial_capital * (self.position_size_bps / 10000) * signal.strength
        quantity = position_value / current_price
        
        # Determine order side
        side = OrderSide.BUY if signal.direction == 'up' else OrderSide.SELL
        
        # Submit market order for immediate execution
        try:
            order = await self.trading_engine.oms.submit_order(
                symbol=symbol,
                side=side,
                order_type=OrderType.MARKET,
                quantity=quantity,
                strategy_id=self.strategy_id
            )
            
            if order.status.value in ['filled', 'partially_filled']:
                # Track position
                position = {
                    'order_id': order.order_id,
                    'entry_price': order.avg_fill_price,
                    'quantity': order.filled_quantity,
                    'side': side.value,
                    'entry_time': order.timestamp,
                    'expected_exit_time': order.timestamp + timedelta(milliseconds=signal.expected_duration_ms),
                    'signal_strength': signal.strength,
                    'signal_confidence': signal.confidence
                }
                
                self.active_positions[symbol].append(position)
                self.daily_trade_count += 1
                
                # Schedule exit
                asyncio.create_task(self._schedule_exit(symbol, position))
                
                self.logger.debug(f"HF momentum trade executed: {symbol} {side.value} {quantity:.2f} @ {order.avg_fill_price:.6f}")
                
        except Exception as e:
            self.logger.error(f"Error executing HF signal: {e}")
    
    async def _schedule_exit(self, symbol: str, position: Dict):
        """Schedule position exit based on signal duration"""
        try:
            # Wait for expected duration
            wait_time = position['expected_exit_time'] - datetime.now()
            wait_seconds = max(0.1, wait_time.total_seconds())
            
            await asyncio.sleep(wait_seconds)
            
            # Check if position still exists
            if position not in self.active_positions[symbol]:
                return
            
            # Exit position
            exit_side = OrderSide.SELL if position['side'] == 'buy' else OrderSide.BUY
            
            exit_order = await self.trading_engine.oms.submit_order(
                symbol=symbol,
                side=exit_side,
                order_type=OrderType.MARKET,
                quantity=position['quantity'],
                strategy_id=self.strategy_id
            )
            
            if exit_order.status.value in ['filled', 'partially_filled']:
                # Calculate PnL
                if position['side'] == 'buy':
                    pnl = (exit_order.avg_fill_price - position['entry_price']) * position['quantity']
                else:
                    pnl = (position['entry_price'] - exit_order.avg_fill_price) * position['quantity']
                
                # Update tracking
                self.total_pnl += pnl
                self.current_daily_pnl += pnl
                self.trades_executed += 1
                
                if pnl > 0:
                    self.win_count += 1
                else:
                    self.loss_count += 1
                
                # Remove position
                self.active_positions[symbol].remove(position)
                
                self.logger.debug(f"HF momentum exit: {symbol} PnL: ${pnl:.2f}")
                
        except Exception as e:
            self.logger.error(f"Error in scheduled exit: {e}")
    
    async def generate_signals(self) -> List[Dict]:
        """Generate signals for compatibility with main engine"""
        # This strategy processes signals in real-time via process_tick
        # Return empty list as signals are handled immediately
        return []
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate high-frequency strategy performance metrics"""
        total_closed_trades = self.win_count + self.loss_count
        win_rate = self.win_count / total_closed_trades if total_closed_trades > 0 else 0
        
        avg_execution_time = np.mean(self.execution_times) if self.execution_times else 0
        p95_execution_time = np.percentile(self.execution_times, 95) if self.execution_times else 0
        
        # Calculate Sharpe ratio (simplified for HF)
        if total_closed_trades > 10:
            trade_returns = []  # Would need to track individual trade returns
            sharpe_ratio = 0  # Placeholder
        else:
            sharpe_ratio = 0
        
        return {
            'total_return': self.total_pnl / self.initial_capital,
            'total_pnl': self.total_pnl,
            'win_rate': win_rate,
            'total_trades': self.trades_executed,
            'daily_trade_count': self.daily_trade_count,
            'avg_execution_time_ms': avg_execution_time,
            'p95_execution_time_ms': p95_execution_time,
            'active_positions': sum(len(positions) for positions in self.active_positions.values()),
            'sharpe_ratio': sharpe_ratio,
            'win_count': self.win_count,
            'loss_count': self.loss_count
        }
    
    async def on_day_end(self):
        """Handle end of trading day"""
        # Close all positions
        for symbol in self.symbols:
            for position in self.active_positions[symbol][:]:  # Copy list to avoid modification during iteration
                exit_side = OrderSide.SELL if position['side'] == 'buy' else OrderSide.BUY
                
                try:
                    await self.trading_engine.oms.submit_order(
                        symbol=symbol,
                        side=exit_side,
                        order_type=OrderType.MARKET,
                        quantity=position['quantity'],
                        strategy_id=self.strategy_id
                    )
                except Exception as e:
                    self.logger.error(f"Error closing position at day end: {e}")
        
        # Reset daily counters
        self.daily_trade_count = 0
        self.current_daily_pnl = 0.0
        
        # Log performance
        metrics = self.calculate_performance_metrics()
        self.logger.info(f"HF Momentum daily performance: {metrics}")

# Example usage and testing
async def test_hf_momentum_strategy():
    """Test the high-frequency momentum strategy"""
    symbols = ['BONK', 'WIF', 'POPCAT']
    strategy = HighFrequencyMomentumStrategy(symbols)
    
    # Simulate high-frequency tick data
    base_prices = {'BONK': 0.000035, 'WIF': 2.20, 'POPCAT': 1.15}
    
    for i in range(1000):  # 1000 ticks
        for symbol in symbols:
            # Generate realistic tick data
            if i == 0:
                price = base_prices[symbol]
            else:
                last_tick = strategy.tick_data[symbol][-1] if strategy.tick_data[symbol] else None
                if last_tick:
                    # Add momentum and noise
                    momentum = np.random.normal(0, 0.0001)
                    noise = np.random.normal(0, 0.00005)
                    price = last_tick.price * (1 + momentum + noise)
                else:
                    price = base_prices[symbol]
            
            spread = price * 0.0005  # 5 basis points spread
            volume = np.random.exponential(1000)
            
            tick = TickData(
                symbol=symbol,
                price=price,
                volume=volume,
                timestamp=datetime.now(),
                bid=price - spread/2,
                ask=price + spread/2,
                bid_size=volume * 0.8,
                ask_size=volume * 0.8
            )
            
            await strategy.process_tick(tick)
        
        # Small delay to simulate realistic tick frequency
        await asyncio.sleep(0.001)  # 1ms between ticks
    
    # Print performance
    metrics = strategy.calculate_performance_metrics()
    print(f"HF Momentum Strategy Performance: {metrics}")

if __name__ == "__main__":
    asyncio.run(test_hf_momentum_strategy())
