"""
Comprehensive Strategy Backtester
Tests ALL available strategies with identical conditions and generates rankings
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging
import warnings
warnings.filterwarnings('ignore')

# Import all available strategies
from production_backtesting_system import ProductionBacktestingSystem
from market_making_strategy import MarketMakingStrategy
from statistical_arbitrage_strategy import StatisticalArbitrageStrategy
from high_frequency_momentum_strategy import HighFrequencyMomentumStrategy
from enhanced_momentum_strategy import EnhancedMomentumStrategy
from enhanced_mean_reversion_strategy import EnhancedMeanReversionStrategy

class ComprehensiveStrategyBacktester:
    """Comprehensive backtesting system for all available strategies"""
    
    def __init__(self, initial_capital: float = 1000000):
        self.initial_capital = initial_capital
        self.logger = logging.getLogger(__name__)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Strategy configurations
        self.strategy_configs = {
            'statistical_arbitrage': {
                'class': StatisticalArbitrageStrategy,
                'allocation': 0.2,
                'description': 'Pairs trading with cointegration analysis'
            },
            'market_making': {
                'class': MarketMakingStrategy,
                'allocation': 0.2,
                'description': 'Avellaneda-Stoikov market making'
            },
            'hf_momentum': {
                'class': HighFrequencyMomentumStrategy,
                'allocation': 0.2,
                'description': 'High frequency momentum trading'
            },
            'enhanced_momentum': {
                'class': EnhancedMomentumStrategy,
                'allocation': 0.2,
                'description': 'Enhanced momentum with GMGN.ai integration'
            },
            'enhanced_mean_reversion': {
                'class': EnhancedMeanReversionStrategy,
                'allocation': 0.2,
                'description': 'Enhanced mean reversion with contrarian signals'
            }
        }
        
        self.results = {}
        
    def generate_market_data(self, symbols: List[str], num_days: int = 30) -> Dict[str, pd.DataFrame]:
        """Generate consistent market data for all strategies"""
        # Use the same data generation as production system
        production_system = ProductionBacktestingSystem()
        return production_system.generate_realistic_market_data(symbols, num_days)
    
    async def backtest_production_strategies(self, market_data: Dict[str, pd.DataFrame]) -> Dict:
        """Backtest production strategies (Statistical Arbitrage, Market Making, HF Momentum)"""
        
        production_system = ProductionBacktestingSystem()
        
        # Run comprehensive backtest
        results = await production_system.run_comprehensive_backtest()
        
        return {
            'statistical_arbitrage': results.get('statistical_arbitrage', {}),
            'market_making': results.get('market_making', {}),
            'hf_momentum': results.get('hf_momentum', {})
        }
    
    def backtest_enhanced_strategies(self, market_data: Dict[str, pd.DataFrame]) -> Dict:
        """Backtest enhanced strategies (Enhanced Momentum, Enhanced Mean Reversion)"""
        
        results = {}
        
        # Convert market data to format expected by enhanced strategies
        price_data = {}
        for symbol, df in market_data.items():
            price_data[symbol] = df[['timestamp', 'price', 'volume']].copy()
            price_data[symbol]['usd_value'] = df['price'] * df['volume']
            price_data[symbol]['liquidity'] = df['liquidity']
        
        # Test Enhanced Momentum Strategy
        try:
            enhanced_momentum = EnhancedMomentumStrategy(
                initial_capital=self.initial_capital * 0.2,
                max_positions=8,
                position_size=0.12
            )
            
            momentum_results = enhanced_momentum.run_backtest(price_data)
            
            results['enhanced_momentum'] = {
                'total_return': (momentum_results.get('final_capital', self.initial_capital * 0.2) - self.initial_capital * 0.2) / (self.initial_capital * 0.2),
                'sharpe_ratio': momentum_results.get('sharpe_ratio', 0),
                'max_drawdown': momentum_results.get('max_drawdown', 0),
                'win_rate': momentum_results.get('win_rate', 0),
                'total_trades': momentum_results.get('total_trades', 0),
                'volatility': momentum_results.get('volatility', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Enhanced Momentum backtest failed: {e}")
            results['enhanced_momentum'] = self._get_default_results()
        
        # Test Enhanced Mean Reversion Strategy
        try:
            enhanced_mean_reversion = EnhancedMeanReversionStrategy(
                initial_capital=self.initial_capital * 0.2,
                max_positions=12,
                position_size=0.08
            )
            
            mean_reversion_results = enhanced_mean_reversion.run_backtest(price_data)
            
            results['enhanced_mean_reversion'] = {
                'total_return': (mean_reversion_results.get('final_capital', self.initial_capital * 0.2) - self.initial_capital * 0.2) / (self.initial_capital * 0.2),
                'sharpe_ratio': mean_reversion_results.get('sharpe_ratio', 0),
                'max_drawdown': mean_reversion_results.get('max_drawdown', 0),
                'win_rate': mean_reversion_results.get('win_rate', 0),
                'total_trades': mean_reversion_results.get('total_trades', 0),
                'volatility': mean_reversion_results.get('volatility', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Enhanced Mean Reversion backtest failed: {e}")
            results['enhanced_mean_reversion'] = self._get_default_results()
        
        return results
    
    def _get_default_results(self) -> Dict:
        """Return default results for failed strategies"""
        return {
            'total_return': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'total_trades': 0,
            'volatility': 0.0
        }
    
    def calculate_strategy_score(self, results: Dict) -> float:
        """Calculate overall strategy performance score"""
        return_score = results.get('total_return', 0) * 100
        sharpe_score = results.get('sharpe_ratio', 0) * 10
        dd_penalty = results.get('max_drawdown', 0) * 100
        win_rate_score = results.get('win_rate', 0) * 100
        
        # Penalize strategies with no trades
        trade_penalty = 0 if results.get('total_trades', 0) > 0 else -50
        
        return return_score + sharpe_score - dd_penalty + win_rate_score + trade_penalty
    
    def rank_strategies(self, all_results: Dict) -> List[Dict]:
        """Rank strategies by performance score"""
        
        rankings = []
        
        for strategy_name, results in all_results.items():
            if strategy_name == 'combined_portfolio':
                continue
                
            score = self.calculate_strategy_score(results)
            
            rankings.append({
                'strategy': strategy_name,
                'score': score,
                'total_return': results.get('total_return', 0) * 100,
                'sharpe_ratio': results.get('sharpe_ratio', 0),
                'max_drawdown': results.get('max_drawdown', 0) * 100,
                'win_rate': results.get('win_rate', 0) * 100,
                'total_trades': results.get('total_trades', 0),
                'description': self.strategy_configs.get(strategy_name, {}).get('description', 'Unknown strategy')
            })
        
        # Sort by score (descending)
        rankings.sort(key=lambda x: x['score'], reverse=True)
        
        return rankings
    
    async def run_comprehensive_backtest(self) -> Dict:
        """Run comprehensive backtest of all available strategies"""
        
        self.logger.info("Starting comprehensive strategy backtest...")
        
        # Generate market data
        symbols = ['BONK', 'WIF', 'POPCAT', 'FARTCOIN', 'SOL']
        market_data = self.generate_market_data(symbols, num_days=30)
        
        # Backtest production strategies
        self.logger.info("Backtesting production strategies...")
        production_results = await self.backtest_production_strategies(market_data)
        
        # Backtest enhanced strategies
        self.logger.info("Backtesting enhanced strategies...")
        enhanced_results = self.backtest_enhanced_strategies(market_data)
        
        # Combine all results
        all_results = {**production_results, **enhanced_results}
        
        # Rank strategies
        rankings = self.rank_strategies(all_results)
        
        # Store results
        self.results = {
            'individual_results': all_results,
            'rankings': rankings,
            'market_data_summary': {
                'symbols': symbols,
                'num_days': 30,
                'total_data_points': len(market_data[symbols[0]])
            }
        }
        
        return self.results
    
    def print_comprehensive_results(self):
        """Print comprehensive results with rankings"""
        
        if not self.results:
            print("No results available. Run backtest first.")
            return
        
        print("\n" + "="*80)
        print("COMPREHENSIVE STRATEGY BACKTEST RESULTS")
        print("="*80)
        
        print(f"\nMarket Data: {self.results['market_data_summary']['symbols']}")
        print(f"Period: {self.results['market_data_summary']['num_days']} days")
        print(f"Data Points: {self.results['market_data_summary']['total_data_points']}")
        
        print(f"\n{'STRATEGY RANKINGS':<50}")
        print("-" * 80)
        print(f"{'Rank':<4} {'Strategy':<25} {'Score':<8} {'Return%':<8} {'Sharpe':<8} {'MaxDD%':<8} {'WinRate%':<8} {'Trades':<8}")
        print("-" * 80)
        
        for i, ranking in enumerate(self.results['rankings'], 1):
            print(f"{i:<4} {ranking['strategy']:<25} {ranking['score']:<8.1f} {ranking['total_return']:<8.2f} "
                  f"{ranking['sharpe_ratio']:<8.3f} {ranking['max_drawdown']:<8.2f} {ranking['win_rate']:<8.1f} {ranking['total_trades']:<8}")
        
        print("\n" + "="*80)
        print("TOP 2 STRATEGY RECOMMENDATIONS")
        print("="*80)
        
        for i, ranking in enumerate(self.results['rankings'][:2], 1):
            print(f"\n#{i} {ranking['strategy'].upper().replace('_', ' ')}")
            print(f"   Description: {ranking['description']}")
            print(f"   Performance Score: {ranking['score']:.1f}")
            print(f"   Total Return: {ranking['total_return']:.2f}%")
            print(f"   Sharpe Ratio: {ranking['sharpe_ratio']:.3f}")
            print(f"   Max Drawdown: {ranking['max_drawdown']:.2f}%")
            print(f"   Win Rate: {ranking['win_rate']:.1f}%")
            print(f"   Total Trades: {ranking['total_trades']}")

# Main execution
async def main():
    backtester = ComprehensiveStrategyBacktester()
    results = await backtester.run_comprehensive_backtest()
    backtester.print_comprehensive_results()
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
