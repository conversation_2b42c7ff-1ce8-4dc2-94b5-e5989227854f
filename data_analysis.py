"""
Comprehensive Data Analysis for Quantitative Trading Strategies
Analyzes blockchain transaction data to extract trading insights
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class DataAnalyzer:
    def __init__(self, csv_file_path):
        """Initialize the data analyzer with the CSV file path"""
        self.csv_file_path = csv_file_path
        self.raw_data = None
        self.token_data = None
        self.price_data = None
        
    def load_data(self):
        """Load and perform initial data cleaning"""
        print("Loading transaction data...")
        self.raw_data = pd.read_csv(self.csv_file_path)
        print(f"Loaded {len(self.raw_data)} transactions")
        
        # Convert timestamp to datetime
        self.raw_data['datetime'] = pd.to_datetime(self.raw_data['time'], unit='s')
        
        # Basic data info
        print("\nDataset Overview:")
        print(f"Date range: {self.raw_data['datetime'].min()} to {self.raw_data['datetime'].max()}")
        print(f"Unique transaction types: {self.raw_data['type'].unique()}")
        print(f"Data shape: {self.raw_data.shape}")
        
        return self.raw_data
    
    def parse_trading_history(self):
        """Parse the JSON trading history to extract token trading data"""
        print("\nParsing trading history...")
        
        token_trades = []
        
        for idx, row in self.raw_data.iterrows():
            if pd.notna(row['history_trade']) and row['history_trade'] != '[]':
                try:
                    # Parse JSON data
                    trade_data = json.loads(row['history_trade'])
                    
                    if isinstance(trade_data, list) and len(trade_data) > 0:
                        for token_info in trade_data:
                            if 'token' in token_info:
                                token = token_info['token']
                                
                                # Helper function to safely convert to float
                                def safe_float(value, default=0.0):
                                    try:
                                        if value is None or value == '':
                                            return default
                                        return float(value)
                                    except (ValueError, TypeError):
                                        return default

                                # Helper function to safely convert to int
                                def safe_int(value, default=0):
                                    try:
                                        if value is None or value == '':
                                            return default
                                        return int(value)
                                    except (ValueError, TypeError):
                                        return default

                                trade_record = {
                                    'timestamp': row['time'],
                                    'datetime': row['datetime'],
                                    'transaction_id': idx,
                                    'token_address': token.get('address', ''),
                                    'symbol': token.get('symbol', ''),
                                    'name': token.get('name', ''),
                                    'price': safe_float(token_info.get('price')),  # Price is in token_info, not token
                                    'price_change_6h': safe_float(token.get('price_change_6h')),
                                    'balance': safe_float(token_info.get('balance')),
                                    'usd_value': safe_float(token_info.get('usd_value')),
                                    'realized_profit': safe_float(token_info.get('realized_profit')),
                                    'realized_pnl': safe_float(token_info.get('realized_pnl')),
                                    'unrealized_profit': safe_float(token_info.get('unrealized_profit')),
                                    'total_profit': safe_float(token_info.get('total_profit')),
                                    'avg_cost': safe_float(token_info.get('avg_cost')),
                                    'avg_sold': safe_float(token_info.get('avg_sold')),
                                    'buy_30d': safe_int(token_info.get('buy_30d')),
                                    'sell_30d': safe_int(token_info.get('sell_30d')),
                                    'liquidity': safe_float(token_info.get('liquidity')),
                                    'history_bought_cost': safe_float(token_info.get('history_bought_cost')),
                                    'history_sold_income': safe_float(token_info.get('history_sold_income')),
                                }
                                
                                token_trades.append(trade_record)
                                
                except (json.JSONDecodeError, KeyError, ValueError) as e:
                    continue
        
        self.token_data = pd.DataFrame(token_trades)
        print(f"Extracted {len(self.token_data)} token trading records")
        print(f"Unique tokens: {self.token_data['symbol'].nunique()}")
        
        return self.token_data
    
    def create_price_series(self):
        """Create price time series for each token"""
        if self.token_data is None:
            self.parse_trading_history()
        
        print("\nCreating price time series...")
        
        # Filter tokens with sufficient data
        token_counts = self.token_data['symbol'].value_counts()
        active_tokens = token_counts[token_counts >= 5].index.tolist()
        
        print(f"Tokens with sufficient data (>=5 records): {len(active_tokens)}")
        
        # Create price series for active tokens
        price_series = {}
        
        for token in active_tokens:
            token_df = self.token_data[self.token_data['symbol'] == token].copy()
            token_df = token_df.sort_values('datetime')
            token_df = token_df.drop_duplicates(subset=['datetime'], keep='last')
            
            if len(token_df) >= 5:  # Minimum data points
                price_series[token] = token_df[['datetime', 'price', 'usd_value', 'total_profit']].copy()
        
        self.price_data = price_series
        print(f"Created price series for {len(price_series)} tokens")
        
        return price_series
    
    def analyze_trading_patterns(self):
        """Analyze trading patterns and profitability"""
        if self.token_data is None:
            self.parse_trading_history()
        
        print("\n" + "="*50)
        print("TRADING PATTERN ANALYSIS")
        print("="*50)
        
        # Overall statistics
        total_trades = len(self.token_data)
        profitable_trades = len(self.token_data[self.token_data['total_profit'] > 0])
        
        print(f"\nOverall Trading Statistics:")
        print(f"Total token positions: {total_trades}")
        print(f"Profitable positions: {profitable_trades} ({profitable_trades/total_trades*100:.1f}%)")
        print(f"Average profit per position: ${self.token_data['total_profit'].mean():.2f}")
        print(f"Total profit: ${self.token_data['total_profit'].sum():.2f}")
        
        # Top performing tokens
        print(f"\nTop 10 Most Profitable Tokens:")
        top_profitable = self.token_data.groupby('symbol')['total_profit'].sum().sort_values(ascending=False).head(10)
        for token, profit in top_profitable.items():
            print(f"{token}: ${profit:.2f}")
        
        # Trading frequency analysis
        print(f"\nTrading Frequency Analysis:")
        trading_freq = self.token_data.groupby('symbol').agg({
            'buy_30d': 'mean',
            'sell_30d': 'mean',
            'total_profit': 'sum'
        }).sort_values('total_profit', ascending=False)
        
        print(f"Average buys per month: {trading_freq['buy_30d'].mean():.2f}")
        print(f"Average sells per month: {trading_freq['sell_30d'].mean():.2f}")
        
        return {
            'total_trades': total_trades,
            'win_rate': profitable_trades/total_trades,
            'avg_profit': self.token_data['total_profit'].mean(),
            'total_profit': self.token_data['total_profit'].sum(),
            'top_tokens': top_profitable
        }
    
    def calculate_technical_indicators(self, token_symbol, window_short=5, window_long=20):
        """Calculate technical indicators for a specific token"""
        if self.price_data is None:
            self.create_price_series()
        
        if token_symbol not in self.price_data:
            return None
        
        df = self.price_data[token_symbol].copy()
        df = df.sort_values('datetime')
        
        # Moving averages
        df['ma_short'] = df['price'].rolling(window=window_short).mean()
        df['ma_long'] = df['price'].rolling(window=window_long).mean()
        
        # Price momentum
        df['momentum'] = df['price'].pct_change(periods=3)
        
        # Volatility
        df['volatility'] = df['price'].rolling(window=10).std()
        
        # RSI-like indicator
        price_changes = df['price'].diff()
        gains = price_changes.where(price_changes > 0, 0)
        losses = -price_changes.where(price_changes < 0, 0)
        
        avg_gains = gains.rolling(window=14).mean()
        avg_losses = losses.rolling(window=14).mean()
        
        rs = avg_gains / avg_losses
        df['rsi'] = 100 - (100 / (1 + rs))
        
        return df
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        print("\n" + "="*60)
        print("COMPREHENSIVE DATA ANALYSIS REPORT")
        print("="*60)
        
        # Load and analyze data
        self.load_data()
        self.parse_trading_history()
        self.create_price_series()
        patterns = self.analyze_trading_patterns()
        
        # Market insights
        print(f"\nMarket Insights:")
        print(f"- Dataset spans {(self.raw_data['datetime'].max() - self.raw_data['datetime'].min()).days} days")
        print(f"- {len(self.price_data)} tokens have sufficient trading data")
        print(f"- Overall win rate: {patterns['win_rate']*100:.1f}%")
        print(f"- Average profit per trade: ${patterns['avg_profit']:.2f}")
        
        # Strategy recommendations
        print(f"\nStrategy Development Recommendations:")
        print(f"1. Momentum Strategy: Focus on tokens with consistent price trends")
        print(f"2. Mean Reversion: Target tokens with high volatility and reversion patterns")
        print(f"3. Risk Management: Implement stop-losses given {(1-patterns['win_rate'])*100:.1f}% loss rate")
        
        return patterns

if __name__ == "__main__":
    # Initialize analyzer
    analyzer = DataAnalyzer("Quant test.csv")
    
    # Generate comprehensive report
    report = analyzer.generate_summary_report()
    
    print(f"\nData analysis complete. Ready for strategy development.")
