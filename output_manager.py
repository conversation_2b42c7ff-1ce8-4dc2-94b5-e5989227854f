"""
Output Manager for Enhanced Quantitative Trading System
Handles comprehensive file generation, logging, and results organization
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class OutputManager:
    def __init__(self, base_dir="trading_results"):
        """
        Initialize output manager with structured directory system
        
        Args:
            base_dir: Base directory for all output files
        """
        self.base_dir = Path(base_dir)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create directory structure
        self.directories = {
            'results': self.base_dir / 'results' / self.timestamp,
            'trades': self.base_dir / 'trades' / self.timestamp,
            'performance': self.base_dir / 'performance' / self.timestamp,
            'gmgn_data': self.base_dir / 'gmgn_data' / self.timestamp,
            'logs': self.base_dir / 'logs' / self.timestamp,
            'reports': self.base_dir / 'reports' / self.timestamp
        }
        
        # Create all directories
        for dir_path in self.directories.values():
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.setup_logging()
        
        # Initialize data storage
        self.trade_records = []
        self.performance_records = []
        self.gmgn_records = []
        self.strategy_decisions = []
        
        self.logger.info(f"Output Manager initialized with timestamp: {self.timestamp}")
    
    def setup_logging(self):
        """Setup comprehensive logging system"""
        log_file = self.directories['logs'] / f"trading_system_{self.timestamp}.log"
        
        # Create logger
        self.logger = logging.getLogger('TradingSystem')
        self.logger.setLevel(logging.INFO)
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # File handler for detailed logs
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(detailed_formatter)
        
        # Console handler for important messages
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        console_handler.setFormatter(simple_formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # Create separate loggers for different components
        self.setup_component_loggers()
    
    def setup_component_loggers(self):
        """Setup specialized loggers for different components"""
        components = ['strategy', 'gmgn', 'risk', 'performance']
        
        for component in components:
            logger = logging.getLogger(f'TradingSystem.{component}')
            logger.setLevel(logging.INFO)
            
            # Component-specific log file
            log_file = self.directories['logs'] / f"{component}_{self.timestamp}.log"
            handler = logging.FileHandler(log_file)
            handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            ))
            logger.addHandler(handler)
            
            # Store logger reference
            setattr(self, f'{component}_logger', logger)
    
    def log_trade(self, trade_data, strategy_name):
        """Log individual trade with comprehensive details"""
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'strategy': strategy_name,
            'symbol': trade_data.get('symbol', ''),
            'action': trade_data.get('action', ''),
            'price': trade_data.get('price', 0),
            'shares': trade_data.get('shares', 0),
            'value': trade_data.get('value', 0),
            'profit_loss': trade_data.get('profit_loss', 0),
            'return_pct': trade_data.get('return_pct', 0),
            'exit_reason': trade_data.get('exit_reason', ''),
            'days_held': trade_data.get('days_held', 0),
            'capital_remaining': trade_data.get('capital_remaining', 0),
            'gmgn_score': trade_data.get('gmgn_score', 0),
            'smart_money_signals': trade_data.get('smart_money_signals', 0),
            'market_sentiment': trade_data.get('market_sentiment', 'neutral')
        }
        
        self.trade_records.append(trade_record)
        
        # Log to strategy logger
        self.strategy_logger.info(
            f"Trade executed - {trade_data.get('action', '')} {trade_data.get('symbol', '')} "
            f"at ${trade_data.get('price', 0):.6f}, P&L: ${trade_data.get('profit_loss', 0):.2f}"
        )
    
    def log_performance_snapshot(self, strategy_name, metrics):
        """Log performance snapshot with detailed metrics"""
        performance_record = {
            'timestamp': datetime.now().isoformat(),
            'strategy': strategy_name,
            'total_capital': metrics.get('final_capital', 0),
            'total_return': metrics.get('total_return', 0),
            'total_profit': metrics.get('total_profit', 0),
            'sharpe_ratio': metrics.get('sharpe_ratio', 0),
            'win_rate': metrics.get('win_rate', 0),
            'max_drawdown': metrics.get('max_drawdown', 0),
            'volatility': metrics.get('volatility', 0),
            'total_trades': metrics.get('total_trades', 0),
            'avg_return_per_trade': metrics.get('avg_return_per_trade', 0),
            'avg_days_held': metrics.get('avg_days_held', 0)
        }
        
        self.performance_records.append(performance_record)
        
        # Log to performance logger
        self.performance_logger.info(
            f"Performance snapshot - {strategy_name}: "
            f"Return: {metrics.get('total_return', 0)*100:.2f}%, "
            f"Sharpe: {metrics.get('sharpe_ratio', 0):.3f}, "
            f"Trades: {metrics.get('total_trades', 0)}"
        )
    
    def log_gmgn_data(self, gmgn_data, data_type):
        """Log GMGN.ai data with structured format"""
        gmgn_record = {
            'timestamp': datetime.now().isoformat(),
            'data_type': data_type,
            'data': gmgn_data
        }
        
        self.gmgn_records.append(gmgn_record)
        
        # Log to GMGN logger
        if data_type == 'wallet_analysis':
            self.gmgn_logger.info(
                f"Wallet analysis updated - Score: {gmgn_data.get('smart_money_score', 0)}/10, "
                f"Win Rate: {gmgn_data.get('pnl_30d', {}).get('win_rate', 0)*100:.1f}%"
            )
        elif data_type == 'smart_signals':
            self.gmgn_logger.info(f"Smart money signals updated - {len(gmgn_data)} signals")
        elif data_type == 'market_sentiment':
            self.gmgn_logger.info(
                f"Market sentiment: {gmgn_data.get('sentiment', 'neutral')} "
                f"({gmgn_data.get('confidence', 0)*100:.1f}% confidence)"
            )
    
    def log_strategy_decision(self, strategy_name, decision_data):
        """Log strategy decision-making process"""
        decision_record = {
            'timestamp': datetime.now().isoformat(),
            'strategy': strategy_name,
            'token': decision_data.get('token', ''),
            'decision': decision_data.get('decision', ''),
            'reason': decision_data.get('reason', ''),
            'score': decision_data.get('score', 0),
            'conditions_met': decision_data.get('conditions_met', []),
            'market_conditions': decision_data.get('market_conditions', {}),
            'gmgn_influence': decision_data.get('gmgn_influence', 0)
        }
        
        self.strategy_decisions.append(decision_record)
        
        # Log to strategy logger
        self.strategy_logger.info(
            f"Strategy decision - {strategy_name}: {decision_data.get('decision', '')} "
            f"{decision_data.get('token', '')} (Score: {decision_data.get('score', 0):.3f})"
        )
    
    def save_trade_records(self, strategy_name):
        """Save trade records to CSV and JSON files"""
        if not self.trade_records:
            return
        
        # Filter trades for this strategy
        strategy_trades = [t for t in self.trade_records if t['strategy'] == strategy_name]
        
        if not strategy_trades:
            return
        
        # Save as CSV
        df = pd.DataFrame(strategy_trades)
        csv_file = self.directories['trades'] / f"{strategy_name}_trades_{self.timestamp}.csv"
        df.to_csv(csv_file, index=False)
        
        # Save as JSON
        json_file = self.directories['trades'] / f"{strategy_name}_trades_{self.timestamp}.json"
        with open(json_file, 'w') as f:
            json.dump(strategy_trades, f, indent=2, default=str)
        
        self.logger.info(f"Saved {len(strategy_trades)} trade records for {strategy_name}")
    
    def save_performance_metrics(self, strategy_name, detailed_metrics):
        """Save comprehensive performance metrics"""
        # Save detailed metrics as JSON
        metrics_file = self.directories['performance'] / f"{strategy_name}_metrics_{self.timestamp}.json"
        with open(metrics_file, 'w') as f:
            json.dump(detailed_metrics, f, indent=2, default=str)
        
        # Save performance time series if available
        if self.performance_records:
            strategy_performance = [p for p in self.performance_records if p['strategy'] == strategy_name]
            if strategy_performance:
                df = pd.DataFrame(strategy_performance)
                csv_file = self.directories['performance'] / f"{strategy_name}_performance_{self.timestamp}.csv"
                df.to_csv(csv_file, index=False)
        
        self.logger.info(f"Saved performance metrics for {strategy_name}")
    
    def save_gmgn_data(self):
        """Save all GMGN.ai data records"""
        if not self.gmgn_records:
            return
        
        # Save all GMGN data as JSON
        gmgn_file = self.directories['gmgn_data'] / f"gmgn_data_{self.timestamp}.json"
        with open(gmgn_file, 'w') as f:
            json.dump(self.gmgn_records, f, indent=2, default=str)
        
        # Create separate files for different data types
        data_types = set(record['data_type'] for record in self.gmgn_records)
        
        for data_type in data_types:
            type_records = [r for r in self.gmgn_records if r['data_type'] == data_type]
            type_file = self.directories['gmgn_data'] / f"{data_type}_{self.timestamp}.json"
            with open(type_file, 'w') as f:
                json.dump(type_records, f, indent=2, default=str)
        
        self.logger.info(f"Saved {len(self.gmgn_records)} GMGN.ai data records")
    
    def save_strategy_decisions(self):
        """Save strategy decision logs"""
        if not self.strategy_decisions:
            return
        
        # Save as JSON
        decisions_file = self.directories['results'] / f"strategy_decisions_{self.timestamp}.json"
        with open(decisions_file, 'w') as f:
            json.dump(self.strategy_decisions, f, indent=2, default=str)
        
        # Save as CSV for analysis
        df = pd.DataFrame(self.strategy_decisions)
        csv_file = self.directories['results'] / f"strategy_decisions_{self.timestamp}.csv"
        df.to_csv(csv_file, index=False)
        
        self.logger.info(f"Saved {len(self.strategy_decisions)} strategy decisions")
    
    def generate_summary_report(self, results_data):
        """Generate comprehensive summary report"""
        report = {
            'execution_summary': {
                'timestamp': self.timestamp,
                'execution_date': datetime.now().isoformat(),
                'total_strategies': len([k for k in results_data.keys() if 'strategy' in k.lower()]),
                'total_trades': len(self.trade_records),
                'total_gmgn_records': len(self.gmgn_records)
            },
            'strategy_performance': {},
            'gmgn_integration': {},
            'risk_analysis': {},
            'recommendations': {}
        }
        
        # Add strategy performance data
        for key, value in results_data.items():
            if 'enhanced' in key and isinstance(value, dict):
                strategy_name = key.replace('_', ' ').title()
                report['strategy_performance'][strategy_name] = {
                    'total_return': value.get('total_return', 0),
                    'sharpe_ratio': value.get('sharpe_ratio', 0),
                    'win_rate': value.get('win_rate', 0),
                    'max_drawdown': value.get('max_drawdown', 0),
                    'total_trades': value.get('total_trades', 0)
                }
        
        # Add GMGN integration analysis
        if 'gmgn_data' in results_data:
            gmgn_data = results_data['gmgn_data']
            report['gmgn_integration'] = {
                'smart_money_score': gmgn_data.get('smart_money_score', 0),
                'wallet_win_rate': gmgn_data.get('pnl_30d', {}).get('win_rate', 0),
                'integration_effectiveness': self.calculate_gmgn_effectiveness(results_data)
            }
        
        # Save report
        report_file = self.directories['reports'] / f"summary_report_{self.timestamp}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Generate human-readable report
        self.generate_readable_report(report)
        
        return report
    
    def calculate_gmgn_effectiveness(self, results_data):
        """Calculate GMGN.ai integration effectiveness"""
        # Simplified effectiveness calculation
        gmgn_trades = len([t for t in self.trade_records if t.get('gmgn_score', 0) > 0])
        total_trades = len(self.trade_records)
        
        if total_trades == 0:
            return 0
        
        return gmgn_trades / total_trades
    
    def generate_readable_report(self, report_data):
        """Generate human-readable summary report"""
        report_file = self.directories['reports'] / f"summary_report_{self.timestamp}.txt"
        
        with open(report_file, 'w') as f:
            f.write("="*80 + "\n")
            f.write("ENHANCED QUANTITATIVE TRADING SYSTEM - EXECUTION REPORT\n")
            f.write("="*80 + "\n")
            f.write(f"Execution Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Report ID: {self.timestamp}\n\n")
            
            # Strategy Performance
            f.write("STRATEGY PERFORMANCE SUMMARY\n")
            f.write("-" * 40 + "\n")
            for strategy, metrics in report_data['strategy_performance'].items():
                f.write(f"\n{strategy}:\n")
                f.write(f"  Total Return: {metrics['total_return']*100:.2f}%\n")
                f.write(f"  Sharpe Ratio: {metrics['sharpe_ratio']:.3f}\n")
                f.write(f"  Win Rate: {metrics['win_rate']*100:.1f}%\n")
                f.write(f"  Max Drawdown: {metrics['max_drawdown']*100:.2f}%\n")
                f.write(f"  Total Trades: {metrics['total_trades']}\n")
            
            # GMGN Integration
            f.write(f"\nGMGN.AI INTEGRATION ANALYSIS\n")
            f.write("-" * 40 + "\n")
            gmgn_data = report_data['gmgn_integration']
            f.write(f"Smart Money Score: {gmgn_data.get('smart_money_score', 0)}/10\n")
            f.write(f"Wallet Win Rate: {gmgn_data.get('wallet_win_rate', 0)*100:.1f}%\n")
            f.write(f"Integration Effectiveness: {gmgn_data.get('integration_effectiveness', 0)*100:.1f}%\n")
            
            # File locations
            f.write(f"\nOUTPUT FILES LOCATION\n")
            f.write("-" * 40 + "\n")
            f.write(f"Base Directory: {self.base_dir}\n")
            f.write(f"Results: {self.directories['results']}\n")
            f.write(f"Trade Records: {self.directories['trades']}\n")
            f.write(f"Performance Data: {self.directories['performance']}\n")
            f.write(f"GMGN Data: {self.directories['gmgn_data']}\n")
            f.write(f"Logs: {self.directories['logs']}\n")
        
        self.logger.info(f"Generated readable summary report")
    
    def finalize_output(self, results_data):
        """Finalize all output files and generate summary"""
        self.logger.info("Finalizing output files...")
        
        # Save all data
        for strategy in ['enhanced_momentum', 'enhanced_mean_reversion']:
            if strategy in results_data:
                strategy_name = strategy.replace('_', ' ').title()
                self.save_trade_records(strategy_name)
                self.save_performance_metrics(strategy_name, results_data[strategy])
        
        self.save_gmgn_data()
        self.save_strategy_decisions()
        
        # Generate summary report
        summary = self.generate_summary_report(results_data)
        
        self.logger.info(f"Output finalization complete. Files saved to: {self.base_dir}")
        
        return summary

if __name__ == "__main__":
    # Test the output manager
    output_manager = OutputManager()
    
    # Test logging
    test_trade = {
        'symbol': 'TEST',
        'action': 'BUY',
        'price': 1.23,
        'shares': 100,
        'value': 123,
        'profit_loss': 0,
        'return_pct': 0
    }
    
    output_manager.log_trade(test_trade, 'test_strategy')
    output_manager.save_trade_records('test_strategy')
    
    print("Output Manager test completed successfully!")
