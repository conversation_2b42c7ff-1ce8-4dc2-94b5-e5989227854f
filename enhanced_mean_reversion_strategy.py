"""
Enhanced Mean Reversion Strategy with GMGN.ai Integration
Combines statistical arbitrage with smart money contrarian signals
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from gmgn_integration import GMGNIntegration
from output_manager import OutputManager

class EnhancedMeanReversionStrategy:
    def __init__(self, initial_capital=100000, max_positions=12, position_size=0.08, output_manager=None):
        """
        Enhanced Mean Reversion Strategy with GMGN.ai Integration

        Args:
            initial_capital: Starting capital in USD
            max_positions: Maximum number of concurrent positions
            position_size: Fraction of capital per position
            output_manager: OutputManager instance for logging and file generation
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_positions = max_positions
        self.position_size = position_size
        self.positions = {}
        self.trade_history = []
        self.performance_metrics = {}

        # Output management
        self.output_manager = output_manager or OutputManager()
        self.strategy_name = "Enhanced Mean Reversion"
        
        # Enhanced strategy parameters
        self.oversold_threshold = -0.15  # 15% below mean
        self.overbought_threshold = 0.12  # 12% above mean
        self.stop_loss = 0.06  # 6% stop loss (very tight)
        self.take_profit = 0.18  # 18% take profit
        self.min_liquidity = 25000  # Minimum liquidity
        self.lookback_period = 7  # Days for mean calculation
        self.volatility_threshold = 0.03  # Minimum volatility
        
        # GMGN.ai integration
        self.gmgn = GMGNIntegration()
        self.contrarian_weight = 0.5  # Weight for contrarian signals
        self.statistical_weight = 0.3  # Weight for statistical measures
        self.sentiment_weight = 0.2  # Weight for sentiment analysis
        
        # Smart money contrarian criteria
        self.min_smart_money_score = 6.5  # Lower threshold for contrarian
        self.contrarian_confidence = 0.6  # Confidence for contrarian trades
        
    def get_gmgn_contrarian_signals(self):
        """Get contrarian signals from GMGN.ai integration"""
        try:
            # Get all GMGN data
            smart_signals = self.gmgn.get_smart_money_signals()
            trending_tokens = self.gmgn.get_trending_tokens(30)
            market_sentiment = self.gmgn.get_market_sentiment()
            wallet_analysis = self.gmgn.get_wallet_analysis()
            
            # Identify contrarian opportunities
            contrarian_signals = []
            
            # Look for tokens that smart money is selling (potential oversold)
            for signal in smart_signals:
                if signal['action'] == 'SELL' and signal['confidence_score'] >= self.contrarian_confidence:
                    contrarian_signals.append({
                        'token_symbol': signal['token_symbol'],
                        'signal_type': 'smart_money_sell',
                        'strength': signal['confidence_score'],
                        'timestamp': signal['timestamp']
                    })
            
            # Look for trending tokens with negative momentum (potential reversal)
            for token in trending_tokens:
                if (token['price_change_24h'] < -0.1 and  # Down 10%+
                    token['smart_money_activity'] in ['high', 'very_high']):
                    contrarian_signals.append({
                        'token_symbol': token['symbol'],
                        'signal_type': 'negative_momentum_reversal',
                        'strength': abs(token['price_change_24h']),
                        'timestamp': datetime.now()
                    })
            
            return {
                'smart_signals': smart_signals,
                'trending_tokens': trending_tokens,
                'market_sentiment': market_sentiment,
                'wallet_analysis': wallet_analysis,
                'contrarian_signals': contrarian_signals
            }
            
        except Exception as e:
            print(f"Error getting GMGN contrarian signals: {e}")
            return {
                'smart_signals': [],
                'trending_tokens': [],
                'market_sentiment': {'sentiment': 'neutral', 'confidence': 0.5},
                'wallet_analysis': None,
                'contrarian_signals': []
            }
    
    def calculate_enhanced_mean_reversion_score(self, token_symbol, price_data, gmgn_data):
        """Calculate enhanced mean reversion score with GMGN.ai data"""
        if len(price_data) < self.lookback_period:
            return 0, 0, 0
        
        # Statistical mean reversion analysis
        recent_prices = price_data['price'].tail(self.lookback_period)
        current_price = recent_prices.iloc[-1]
        
        mean_price = recent_prices.mean()
        std_price = recent_prices.std()
        
        if std_price == 0:
            return 0, 0, 0
        
        # Z-score and price deviation
        z_score = (current_price - mean_price) / std_price
        price_deviation = (current_price - mean_price) / mean_price
        
        # Volatility measure
        returns = recent_prices.pct_change().dropna()
        volatility = returns.std()
        
        # Statistical score
        statistical_score = abs(z_score) / 3.0  # Normalize z-score
        
        # GMGN.ai contrarian score
        contrarian_score = 0
        for signal in gmgn_data.get('contrarian_signals', []):
            if signal['token_symbol'] == token_symbol:
                if signal['signal_type'] == 'smart_money_sell':
                    contrarian_score = max(contrarian_score, signal['strength'])
                elif signal['signal_type'] == 'negative_momentum_reversal':
                    contrarian_score = max(contrarian_score, min(signal['strength'], 1.0))
        
        # Market sentiment contrarian adjustment
        sentiment_score = 0
        market_sentiment = gmgn_data.get('market_sentiment', {})
        if market_sentiment.get('sentiment') == 'bearish':
            # In bearish markets, look for oversold opportunities
            if price_deviation < self.oversold_threshold:
                sentiment_score = market_sentiment.get('confidence', 0) * 0.5
        elif market_sentiment.get('sentiment') == 'bullish':
            # In bullish markets, be more cautious with mean reversion
            sentiment_score = -market_sentiment.get('confidence', 0) * 0.2
        
        # Combined enhanced score
        enhanced_score = (
            statistical_score * self.statistical_weight +
            contrarian_score * self.contrarian_weight +
            sentiment_score * self.sentiment_weight
        )
        
        return z_score, price_deviation, enhanced_score
    
    def should_enter_position(self, token_symbol, price_data, current_time, gmgn_data):
        """Enhanced entry conditions with GMGN.ai contrarian signals"""
        # Basic checks
        if token_symbol in self.positions:
            return False
        
        if len(self.positions) >= self.max_positions:
            return False
        
        if len(price_data) < self.lookback_period:
            return False
        
        # Liquidity check
        latest_data = price_data.iloc[-1]
        if latest_data.get('liquidity', 0) < self.min_liquidity:
            return False
        
        # Get GMGN data if not provided
        if not gmgn_data:
            gmgn_data = self.get_gmgn_contrarian_signals()
        
        # Calculate enhanced mean reversion metrics
        z_score, price_deviation, enhanced_score = self.calculate_enhanced_mean_reversion_score(
            token_symbol, price_data, gmgn_data
        )
        
        # Volatility check
        returns = price_data['price'].tail(self.lookback_period).pct_change().dropna()
        volatility = returns.std()
        if volatility < self.volatility_threshold:
            return False
        
        # Enhanced entry conditions
        entry_conditions = [
            # Statistical oversold condition
            price_deviation < self.oversold_threshold and z_score < -1.5,
            # Enhanced score threshold
            enhanced_score > 0.3,
            # Security and market checks
            self.check_contrarian_security(token_symbol, gmgn_data),
            self.check_contrarian_market_conditions(gmgn_data)
        ]
        
        return all(entry_conditions)
    
    def check_contrarian_security(self, token_symbol, gmgn_data):
        """Check security for contrarian trades"""
        # More lenient security checks for contrarian trades
        # as we're often buying when others are selling
        try:
            return True  # Simplified for demo
        except:
            return False
    
    def check_contrarian_market_conditions(self, gmgn_data):
        """Check market conditions for contrarian trades"""
        market_sentiment = gmgn_data.get('market_sentiment', {})
        sentiment = market_sentiment.get('sentiment', 'neutral')
        confidence = market_sentiment.get('confidence', 0.5)
        
        # Contrarian trades work better in extreme market conditions
        if sentiment == 'bearish' and confidence > 0.7:
            return True  # Good time for contrarian buying
        elif sentiment == 'bullish' and confidence < 0.8:
            return True  # Neutral to mild bullish is okay
        else:
            return confidence < 0.6  # Low confidence markets are good for mean reversion
    
    def should_exit_position(self, token_symbol, current_price, current_time, price_data, gmgn_data):
        """Enhanced exit conditions with GMGN.ai integration"""
        if token_symbol not in self.positions:
            return False, None
        
        position = self.positions[token_symbol]
        entry_price = position['entry_price']
        current_return = (current_price - entry_price) / entry_price
        
        # Basic exit conditions
        if current_return <= -self.stop_loss:
            return True, "stop_loss"
        
        if current_return >= self.take_profit:
            return True, "take_profit"
        
        # Mean reversion completed check
        if len(price_data) >= self.lookback_period:
            z_score, price_deviation, enhanced_score = self.calculate_enhanced_mean_reversion_score(
                token_symbol, price_data, gmgn_data
            )
            
            # Exit if price has reverted to mean or beyond
            if abs(price_deviation) < 0.03:  # Within 3% of mean
                return True, "mean_reversion_complete"
            
            # Exit if enhanced score suggests reversal is over
            if enhanced_score < 0.1:
                return True, "enhanced_signal_exit"
        
        # Time-based exit (shorter for mean reversion)
        days_held = (current_time - position['entry_time']).days
        if days_held >= 15:
            return True, "time_exit"
        
        # Smart money reversal signals
        for signal in gmgn_data.get('smart_signals', []):
            if (signal['token_symbol'] == token_symbol and 
                signal['action'] == 'BUY' and  # Smart money buying back
                signal['confidence_score'] >= 0.8):
                return True, "smart_money_reversal"
        
        return False, None

    def enter_position(self, token_symbol, price, timestamp, token_data, gmgn_data=None):
        """Enter a new position with enhanced logging"""
        position_value = self.current_capital * self.position_size
        shares = position_value / price

        position = {
            'symbol': token_symbol,
            'entry_price': price,
            'entry_time': timestamp,
            'shares': shares,
            'position_value': position_value,
            'entry_data': token_data,
            'gmgn_data': gmgn_data,
            'entry_reason': 'enhanced_mean_reversion'
        }

        self.positions[token_symbol] = position
        self.current_capital -= position_value

        # Record trade
        trade = {
            'timestamp': timestamp,
            'symbol': token_symbol,
            'action': 'BUY',
            'price': price,
            'shares': shares,
            'value': position_value,
            'capital_remaining': self.current_capital,
            'strategy': 'enhanced_mean_reversion',
            'contrarian_signals': len(gmgn_data.get('contrarian_signals', [])) if gmgn_data else 0
        }
        self.trade_history.append(trade)

        return position

    def exit_position(self, token_symbol, price, timestamp, exit_reason):
        """Exit position with enhanced tracking"""
        if token_symbol not in self.positions:
            return None

        position = self.positions[token_symbol]
        shares = position['shares']
        exit_value = shares * price

        profit_loss = exit_value - position['position_value']
        return_pct = profit_loss / position['position_value']

        self.current_capital += exit_value

        # Record trade
        trade = {
            'timestamp': timestamp,
            'symbol': token_symbol,
            'action': 'SELL',
            'price': price,
            'shares': shares,
            'value': exit_value,
            'profit_loss': profit_loss,
            'return_pct': return_pct,
            'exit_reason': exit_reason,
            'capital_remaining': self.current_capital,
            'days_held': (timestamp - position['entry_time']).days,
            'strategy': 'enhanced_mean_reversion',
            'entry_contrarian_signals': len(position.get('gmgn_data', {}).get('contrarian_signals', []))
        }
        self.trade_history.append(trade)

        del self.positions[token_symbol]
        return trade

    def run_backtest(self, price_data_dict, start_date=None, end_date=None):
        """Run enhanced mean reversion strategy backtest"""
        print("Running Enhanced Mean Reversion Strategy Backtest with GMGN.ai Integration...")

        # Get GMGN contrarian data
        gmgn_data = self.get_gmgn_contrarian_signals()

        # Create timeline
        all_updates = []
        for token, data in price_data_dict.items():
            for _, row in data.iterrows():
                all_updates.append({
                    'timestamp': row['datetime'],
                    'token': token,
                    'price': row['price'],
                    'data': row
                })

        all_updates = sorted(all_updates, key=lambda x: x['timestamp'])

        if start_date:
            all_updates = [u for u in all_updates if u['timestamp'] >= start_date]
        if end_date:
            all_updates = [u for u in all_updates if u['timestamp'] <= end_date]

        print(f"Processing {len(all_updates)} price updates with GMGN.ai contrarian analysis...")

        for i, update in enumerate(all_updates):
            token = update['token']
            price = update['price']
            timestamp = update['timestamp']

            token_history = price_data_dict[token]
            current_history = token_history[token_history['datetime'] <= timestamp]

            # Check exit conditions
            if token in self.positions:
                should_exit, exit_reason = self.should_exit_position(
                    token, price, timestamp, current_history, gmgn_data
                )
                if should_exit:
                    self.exit_position(token, price, timestamp, exit_reason)

            # Check entry conditions
            elif self.should_enter_position(token, current_history, timestamp, gmgn_data):
                self.enter_position(token, price, timestamp, update['data'], gmgn_data)

            if i % 1000 == 0:
                print(f"Processed {i}/{len(all_updates)} updates. Capital: ${self.current_capital:.2f}")

        # Close remaining positions
        final_timestamp = all_updates[-1]['timestamp'] if all_updates else datetime.now()
        for token in list(self.positions.keys()):
            final_price = price_data_dict[token].iloc[-1]['price']
            self.exit_position(token, final_price, final_timestamp, "backtest_end")

        print(f"Enhanced mean reversion backtest complete. Final capital: ${self.current_capital:.2f}")
        return self.calculate_performance_metrics()

    def calculate_performance_metrics(self):
        """Calculate enhanced performance metrics"""
        if not self.trade_history:
            return {}

        trades_df = pd.DataFrame(self.trade_history)
        sell_trades = trades_df[trades_df['action'] == 'SELL'].copy()

        if len(sell_trades) == 0:
            return {'error': 'No completed trades'}

        # Basic metrics
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        total_trades = len(sell_trades)
        winning_trades = len(sell_trades[sell_trades['profit_loss'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # Enhanced metrics
        avg_profit = sell_trades['profit_loss'].mean()
        total_profit = sell_trades['profit_loss'].sum()
        avg_return = sell_trades['return_pct'].mean()

        # Risk metrics
        returns = sell_trades['return_pct']
        volatility = returns.std()
        sharpe_ratio = (avg_return / volatility) if volatility > 0 else 0

        # Contrarian-specific metrics
        contrarian_trades = sell_trades[sell_trades['entry_contrarian_signals'] > 0] if 'entry_contrarian_signals' in sell_trades.columns else pd.DataFrame()
        contrarian_performance = contrarian_trades['return_pct'].mean() if len(contrarian_trades) > 0 else 0

        self.performance_metrics = {
            'total_return': total_return,
            'total_profit': total_profit,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_profit_per_trade': avg_profit,
            'avg_return_per_trade': avg_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'final_capital': self.current_capital,
            'avg_days_held': sell_trades['days_held'].mean(),
            'contrarian_trades': len(contrarian_trades),
            'contrarian_performance': contrarian_performance
        }

        return self.performance_metrics

    def print_performance_report(self):
        """Print enhanced performance report"""
        if not self.performance_metrics:
            self.calculate_performance_metrics()

        metrics = self.performance_metrics

        print("\n" + "="*60)
        print("ENHANCED MEAN REVERSION STRATEGY PERFORMANCE REPORT")
        print("="*60)

        print(f"Initial Capital: ${self.initial_capital:,.2f}")
        print(f"Final Capital: ${metrics.get('final_capital', 0):,.2f}")
        print(f"Total Return: {metrics.get('total_return', 0)*100:.2f}%")
        print(f"Total Profit: ${metrics.get('total_profit', 0):,.2f}")

        print(f"\nTrading Statistics:")
        print(f"Total Trades: {metrics.get('total_trades', 0)}")
        print(f"Win Rate: {metrics.get('win_rate', 0)*100:.1f}%")
        print(f"Average Profit per Trade: ${metrics.get('avg_profit_per_trade', 0):.2f}")
        print(f"Average Return per Trade: {metrics.get('avg_return_per_trade', 0)*100:.2f}%")
        print(f"Average Days Held: {metrics.get('avg_days_held', 0):.1f}")

        print(f"\nGMGN.ai Contrarian Integration Results:")
        print(f"Contrarian-Enhanced Trades: {metrics.get('contrarian_trades', 0)}")
        print(f"Contrarian Performance: {metrics.get('contrarian_performance', 0)*100:.2f}%")

        print(f"\nRisk Metrics:")
        print(f"Volatility: {metrics.get('volatility', 0)*100:.2f}%")
        print(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.3f}")

        return metrics
