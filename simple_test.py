"""
Simple test to create a working example with synthetic data
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from momentum_strategy import MomentumStrategy
from mean_reversion_strategy import MeanReversionStrategy

def create_synthetic_data():
    """Create synthetic price data for testing"""
    print("Creating synthetic cryptocurrency price data...")
    
    # Create date range
    start_date = datetime(2025, 7, 16)
    dates = [start_date + timedelta(hours=i) for i in range(100)]
    
    # Create synthetic tokens with different patterns
    tokens = {
        'MOMENTUM_TOKEN': {
            'pattern': 'trending_up',
            'base_price': 1.0,
            'volatility': 0.02
        },
        'REVERSION_TOKEN': {
            'pattern': 'mean_reverting',
            'base_price': 10.0,
            'volatility': 0.05
        },
        'STABLE_TOKEN': {
            'pattern': 'stable',
            'base_price': 100.0,
            'volatility': 0.01
        }
    }
    
    price_data = {}
    
    for token_name, config in tokens.items():
        prices = []
        current_price = config['base_price']
        
        for i, date in enumerate(dates):
            if config['pattern'] == 'trending_up':
                # Upward trend with some noise
                trend = 0.001 * i  # 0.1% per hour trend
                noise = np.random.normal(0, config['volatility'])
                current_price = config['base_price'] * (1 + trend + noise)
                
            elif config['pattern'] == 'mean_reverting':
                # Mean reverting pattern
                mean_price = config['base_price']
                reversion_speed = 0.1
                noise = np.random.normal(0, config['volatility'])
                current_price = current_price + reversion_speed * (mean_price - current_price) + mean_price * noise
                
            elif config['pattern'] == 'stable':
                # Stable with low volatility
                noise = np.random.normal(0, config['volatility'])
                current_price = config['base_price'] * (1 + noise)
            
            prices.append({
                'datetime': date,
                'price': max(current_price, 0.001),  # Ensure positive prices
                'usd_value': max(current_price, 0.001) * 1000,  # Synthetic volume
                'total_profit': np.random.normal(0, 100),  # Random profit data
                'liquidity': 10000  # High liquidity
            })
        
        price_data[token_name] = pd.DataFrame(prices)
    
    return price_data

def test_strategies():
    """Test both strategies with synthetic data"""
    print("="*60)
    print("TESTING QUANTITATIVE TRADING STRATEGIES")
    print("="*60)
    
    # Create synthetic data
    price_data = create_synthetic_data()
    
    # Initialize strategies
    momentum_strategy = MomentumStrategy(initial_capital=100000)
    mean_reversion_strategy = MeanReversionStrategy(initial_capital=100000)
    
    print(f"\nSynthetic data created:")
    for token, df in price_data.items():
        print(f"  {token}: {len(df)} data points, price range ${df['price'].min():.3f}-${df['price'].max():.3f}")
    
    # Test momentum strategy
    print(f"\n" + "="*40)
    print("TESTING MOMENTUM STRATEGY")
    print("="*40)
    
    momentum_results = momentum_strategy.run_backtest(price_data)
    momentum_strategy.print_performance_report()
    
    # Test mean reversion strategy
    print(f"\n" + "="*40)
    print("TESTING MEAN REVERSION STRATEGY")
    print("="*40)
    
    mean_reversion_results = mean_reversion_strategy.run_backtest(price_data)
    mean_reversion_strategy.print_performance_report()
    
    # Summary
    print(f"\n" + "="*60)
    print("STRATEGY COMPARISON SUMMARY")
    print("="*60)
    
    print(f"Momentum Strategy:")
    print(f"  Total Return: {momentum_results.get('total_return', 0)*100:.2f}%")
    print(f"  Sharpe Ratio: {momentum_results.get('sharpe_ratio', 0):.3f}")
    print(f"  Total Trades: {momentum_results.get('total_trades', 0)}")
    print(f"  Win Rate: {momentum_results.get('win_rate', 0)*100:.1f}%")
    
    print(f"\nMean Reversion Strategy:")
    print(f"  Total Return: {mean_reversion_results.get('total_return', 0)*100:.2f}%")
    print(f"  Sharpe Ratio: {mean_reversion_results.get('sharpe_ratio', 0):.3f}")
    print(f"  Total Trades: {mean_reversion_results.get('total_trades', 0)}")
    print(f"  Win Rate: {mean_reversion_results.get('win_rate', 0)*100:.1f}%")
    
    # Determine better strategy
    momentum_sharpe = momentum_results.get('sharpe_ratio', 0)
    mean_reversion_sharpe = mean_reversion_results.get('sharpe_ratio', 0)
    
    if momentum_sharpe > mean_reversion_sharpe:
        print(f"\n🏆 Momentum Strategy shows superior risk-adjusted returns")
    elif mean_reversion_sharpe > momentum_sharpe:
        print(f"\n🏆 Mean Reversion Strategy shows superior risk-adjusted returns")
    else:
        print(f"\n⚖️  Both strategies show similar performance")
    
    print(f"\n✅ Strategy testing completed successfully!")
    return momentum_results, mean_reversion_results

if __name__ == "__main__":
    np.random.seed(42)  # For reproducible results
    test_strategies()
