"""
Institutional Market Making Strategy
Advanced bid-ask spread capture with sophisticated inventory management
Target: >60% win rate, Sharpe ratio >2.0, <5% max drawdown
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

from production_trading_engine import OrderSide, OrderType, ProductionTradingEngine

@dataclass
class MarketData:
    """Real-time market data structure"""
    symbol: str
    bid: float
    ask: float
    bid_size: float
    ask_size: float
    last_price: float
    volume: float
    timestamp: datetime
    volatility: float = 0.0
    
class InventoryManager:
    """Advanced inventory management for market making"""
    
    def __init__(self, max_inventory: float = 100000, target_inventory: float = 0):
        self.max_inventory = max_inventory
        self.target_inventory = target_inventory
        self.inventory_half_life = 600  # 10 minutes in seconds (match backtest interval)
        self.risk_aversion = 0.1  # Increased risk aversion for better inventory management
        
    def calculate_inventory_skew(self, current_inventory: float, market_data: MarketData) -> float:
        """Calculate inventory skew adjustment for quotes"""
        inventory_ratio = current_inventory / self.max_inventory
        
        # Exponential decay towards target
        time_decay = np.exp(-1 / self.inventory_half_life)
        inventory_pressure = inventory_ratio * (1 - time_decay)
        
        # Risk adjustment based on volatility
        volatility_adjustment = market_data.volatility * self.risk_aversion
        
        # Skew calculation: positive skew means widen ask, tighten bid
        skew = inventory_pressure * volatility_adjustment

        return np.clip(skew, -0.05, 0.05)  # Increased skew limit to 500 basis points for better inventory management
    
    def calculate_optimal_spread(self, market_data: MarketData, current_inventory: float) -> Tuple[float, float]:
        """Calculate optimal bid-ask spread using Avellaneda-Stoikov model"""
        mid_price = (market_data.bid + market_data.ask) / 2
        
        # Base spread from market microstructure - Enhanced for profitability
        base_spread = market_data.ask - market_data.bid
        min_spread = max(base_spread * 0.8, mid_price * 0.0005)  # Minimum 5 basis points for profitability

        # Volatility component - Increased for adverse selection protection
        volatility_spread = market_data.volatility * mid_price * 0.5
        
        # Inventory adjustment
        inventory_skew = self.calculate_inventory_skew(current_inventory, market_data)
        
        # Optimal spread calculation
        optimal_half_spread = min_spread / 2 + volatility_spread + abs(inventory_skew) * mid_price
        
        # Asymmetric quotes based on inventory
        bid_adjustment = -inventory_skew * mid_price
        ask_adjustment = inventory_skew * mid_price
        
        optimal_bid = mid_price - optimal_half_spread + bid_adjustment
        optimal_ask = mid_price + optimal_half_spread + ask_adjustment
        
        return optimal_bid, optimal_ask

class MarketMakingStrategy:
    """Advanced market making strategy with institutional-grade features"""
    
    def __init__(self, symbols: List[str], initial_capital: float = 1000000):
        self.symbols = symbols
        self.initial_capital = initial_capital
        self.strategy_id = "market_making_v1"
        
        # Strategy parameters - Optimized for better performance
        self.max_position_size = initial_capital * 0.1  # 10% of capital per symbol
        self.quote_size = initial_capital * 0.01  # 1% of capital base quote size
        self.min_spread_bps = 8  # Minimum 8 basis points spread for profitability
        self.max_spread_bps = 100  # Increased maximum spread for volatile conditions
        
        # Inventory management
        self.inventory_managers = {
            symbol: InventoryManager(max_inventory=self.max_position_size)
            for symbol in symbols
        }
        
        # Market data storage
        self.market_data: Dict[str, MarketData] = {}
        self.price_history: Dict[str, List[float]] = {symbol: [] for symbol in symbols}
        
        # Performance tracking
        self.trades_executed = 0
        self.total_pnl = 0.0
        self.daily_returns = []
        
        # Risk management - More conservative
        self.max_daily_loss = initial_capital * 0.01  # 1% daily loss limit
        self.current_daily_pnl = 0.0
        
        # Logging
        self.logger = logging.getLogger(f'MarketMaking_{self.strategy_id}')
        
    async def initialize(self, trading_engine: ProductionTradingEngine):
        """Initialize strategy with trading engine"""
        self.trading_engine = trading_engine
        self.logger.info(f"Market Making Strategy initialized for {len(self.symbols)} symbols")
        
    async def generate_signals(self) -> List[Dict]:
        """Generate market making signals"""
        signals = []
        
        for symbol in self.symbols:
            if symbol not in self.market_data:
                continue
                
            try:
                symbol_signals = await self._generate_symbol_signals(symbol)
                signals.extend(symbol_signals)
            except Exception as e:
                self.logger.error(f"Error generating signals for {symbol}: {e}")
        
        return signals
    
    async def _generate_symbol_signals(self, symbol: str) -> List[Dict]:
        """Generate signals for a specific symbol"""
        market_data = self.market_data[symbol]
        current_position = self._get_current_position(symbol)
        
        # Check if we should quote this symbol
        if not self._should_quote(symbol, market_data):
            return []
        
        # Calculate optimal quotes
        inventory_manager = self.inventory_managers[symbol]
        optimal_bid, optimal_ask = inventory_manager.calculate_optimal_spread(
            market_data, current_position
        )
        
        # Determine quote sizes based on market conditions and inventory
        bid_size, ask_size = self._calculate_quote_sizes(symbol, current_position, market_data)
        
        signals = []
        
        # Generate bid signal
        if bid_size > 0 and optimal_bid > 0:
            signals.append({
                'symbol': symbol,
                'side': 'buy',
                'order_type': 'limit',
                'quantity': bid_size,
                'price': optimal_bid,
                'strength': self._calculate_signal_strength(symbol, 'bid'),
                'strategy_type': 'market_making_bid',
                'expected_hold_time': 30  # seconds
            })
        
        # Generate ask signal
        if ask_size > 0 and optimal_ask > 0:
            signals.append({
                'symbol': symbol,
                'side': 'sell',
                'order_type': 'limit',
                'quantity': ask_size,
                'price': optimal_ask,
                'strength': self._calculate_signal_strength(symbol, 'ask'),
                'strategy_type': 'market_making_ask',
                'expected_hold_time': 30  # seconds
            })
        
        return signals
    
    def _should_quote(self, symbol: str, market_data: MarketData) -> bool:
        """Determine if we should provide quotes for this symbol"""
        # Check daily loss limit
        if self.current_daily_pnl < -self.max_daily_loss:
            return False
        
        # Check market conditions
        spread_bps = ((market_data.ask - market_data.bid) / market_data.last_price) * 10000
        
        # Don't quote if spread is too wide (illiquid market)
        if spread_bps > self.max_spread_bps:
            return False

        # Don't quote if spread is too tight (no profit opportunity)
        if spread_bps < self.min_spread_bps:
            return False

        # Check volatility - more lenient threshold for better activity
        if market_data.volatility > 0.15:  # 15% volatility threshold (more lenient)
            return False
        
        return True
    
    def _calculate_quote_sizes(self, symbol: str, current_position: float, 
                             market_data: MarketData) -> Tuple[float, float]:
        """Calculate optimal quote sizes based on inventory and market conditions"""
        base_size = self.quote_size
        
        # Adjust size based on inventory
        inventory_ratio = abs(current_position) / self.max_position_size
        size_adjustment = max(0.1, 1 - inventory_ratio)  # Reduce size as inventory grows
        
        # Adjust size based on market conditions
        volatility_adjustment = max(0.5, 1 - market_data.volatility * 10)
        
        # Adjust size based on spread
        spread_bps = ((market_data.ask - market_data.bid) / market_data.last_price) * 10000
        spread_adjustment = min(2.0, spread_bps / 10)  # Larger size for wider spreads
        
        adjusted_size = base_size * size_adjustment * volatility_adjustment * spread_adjustment
        
        # Inventory-based asymmetric sizing
        if current_position > 0:  # Long inventory - prefer to sell
            bid_size = adjusted_size * 0.5
            ask_size = adjusted_size * 1.5
        elif current_position < 0:  # Short inventory - prefer to buy
            bid_size = adjusted_size * 1.5
            ask_size = adjusted_size * 0.5
        else:  # Neutral inventory
            bid_size = ask_size = adjusted_size
        
        # Ensure we don't exceed position limits
        max_additional_long = max(0, self.max_position_size - current_position)
        max_additional_short = max(0, self.max_position_size + current_position)
        
        bid_size = min(bid_size, max_additional_long)
        ask_size = min(ask_size, max_additional_short)
        
        return bid_size, ask_size
    
    def _calculate_signal_strength(self, symbol: str, side: str) -> float:
        """Calculate signal strength based on market conditions"""
        market_data = self.market_data[symbol]
        
        # Base strength from spread
        spread_bps = ((market_data.ask - market_data.bid) / market_data.last_price) * 10000
        spread_strength = min(1.0, spread_bps / 20)  # Normalize to 0-1
        
        # Volume strength
        volume_strength = min(1.0, market_data.volume / 100000)  # Normalize volume
        
        # Volatility adjustment (lower volatility = higher strength)
        volatility_strength = max(0.1, 1 - market_data.volatility * 5)
        
        # Combine factors
        signal_strength = (spread_strength * 0.4 + volume_strength * 0.3 + volatility_strength * 0.3)
        
        return np.clip(signal_strength, 0.1, 1.0)
    
    def _get_current_position(self, symbol: str) -> float:
        """Get current position for symbol"""
        if hasattr(self, 'trading_engine') and symbol in self.trading_engine.oms.positions:
            return self.trading_engine.oms.positions[symbol].quantity
        return 0.0
    
    async def update_market_data(self, symbol: str, bid: float, ask: float, 
                               last_price: float, volume: float):
        """Update market data for symbol"""
        # Calculate volatility from price history
        self.price_history[symbol].append(last_price)
        if len(self.price_history[symbol]) > 100:
            self.price_history[symbol] = self.price_history[symbol][-100:]
        
        volatility = 0.0
        if len(self.price_history[symbol]) > 10:
            returns = np.diff(np.log(self.price_history[symbol]))
            volatility = np.std(returns) * np.sqrt(252 * 24 * 60)  # Annualized volatility
        
        self.market_data[symbol] = MarketData(
            symbol=symbol,
            bid=bid,
            ask=ask,
            bid_size=1000,  # Placeholder
            ask_size=1000,  # Placeholder
            last_price=last_price,
            volume=volume,
            timestamp=datetime.now(),
            volatility=volatility
        )
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate strategy performance metrics"""
        if len(self.daily_returns) < 2:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'total_trades': self.trades_executed
            }
        
        returns = np.array(self.daily_returns)
        
        total_return = (1 + returns).prod() - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Calculate maximum drawdown
        cumulative_returns = (1 + returns).cumprod()
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / peak
        max_drawdown = np.min(drawdown)
        
        win_rate = len(returns[returns > 0]) / len(returns) if len(returns) > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'win_rate': win_rate,
            'volatility': np.std(returns) * np.sqrt(252),
            'total_trades': self.trades_executed,
            'avg_daily_return': np.mean(returns),
            'total_pnl': self.total_pnl
        }
    
    async def on_trade_executed(self, trade_info: Dict):
        """Handle trade execution callback"""
        self.trades_executed += 1
        
        # Update PnL tracking
        if 'pnl' in trade_info:
            self.total_pnl += trade_info['pnl']
            self.current_daily_pnl += trade_info['pnl']
        
        # Log trade
        self.logger.info(f"Market making trade executed: {trade_info}")
    
    async def on_day_end(self):
        """Handle end of trading day"""
        if self.current_daily_pnl != 0:
            daily_return = self.current_daily_pnl / self.initial_capital
            self.daily_returns.append(daily_return)
        
        # Reset daily tracking
        self.current_daily_pnl = 0.0
        
        # Log daily performance
        metrics = self.calculate_performance_metrics()
        self.logger.info(f"Daily performance: {metrics}")

# Example usage and testing
async def test_market_making_strategy():
    """Test the market making strategy"""
    symbols = ['BONK', 'WIF', 'POPCAT']
    strategy = MarketMakingStrategy(symbols)
    
    # Simulate market data updates
    for i in range(100):
        for symbol in symbols:
            base_price = {'BONK': 0.000035, 'WIF': 2.20, 'POPCAT': 1.15}[symbol]
            price = base_price * (1 + np.random.normal(0, 0.01))
            spread = price * 0.001  # 10 basis points spread
            
            await strategy.update_market_data(
                symbol=symbol,
                bid=price - spread/2,
                ask=price + spread/2,
                last_price=price,
                volume=np.random.uniform(10000, 100000)
            )
        
        # Generate signals
        signals = await strategy.generate_signals()
        print(f"Generated {len(signals)} signals")
        
        await asyncio.sleep(0.1)
    
    # Print performance
    metrics = strategy.calculate_performance_metrics()
    print(f"Strategy Performance: {metrics}")

if __name__ == "__main__":
    asyncio.run(test_market_making_strategy())
