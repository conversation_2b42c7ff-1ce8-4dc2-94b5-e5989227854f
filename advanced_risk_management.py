"""
Advanced Risk Management and Hedging System
Institutional-grade risk controls with delta-neutral strategies and dynamic hedging
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
from scipy import stats
from scipy.optimize import minimize

from production_trading_engine import OrderSide, OrderType, Position

class RiskMetric(Enum):
    VAR_95 = "var_95"
    VAR_99 = "var_99"
    EXPECTED_SHORTFALL = "expected_shortfall"
    MAXIMUM_DRAWDOWN = "maximum_drawdown"
    BETA = "beta"
    CORRELATION = "correlation"

@dataclass
class RiskLimit:
    """Risk limit definition"""
    metric: RiskMetric
    limit_value: float
    warning_threshold: float
    breach_action: str  # 'alert', 'reduce', 'close'

@dataclass
class HedgeInstrument:
    """Hedge instrument definition"""
    symbol: str
    hedge_ratio: float
    correlation: float
    liquidity_score: float
    cost_basis_points: float

class PortfolioRiskAnalyzer:
    """Advanced portfolio risk analysis"""
    
    def __init__(self, confidence_levels: List[float] = [0.95, 0.99]):
        self.confidence_levels = confidence_levels
        self.lookback_period = 252  # 1 year of daily data
        self.min_observations = 30
        
    def calculate_portfolio_var(self, positions: Dict[str, Position], 
                              returns_data: Dict[str, np.ndarray],
                              confidence: float = 0.95) -> Dict:
        """Calculate portfolio Value at Risk using multiple methods"""
        
        if not positions or not returns_data:
            return {}
        
        # Get position weights
        total_value = sum(abs(pos.market_value) for pos in positions.values())
        if total_value == 0:
            return {}
        
        weights = {}
        portfolio_returns = []
        
        # Calculate portfolio returns
        min_length = min(len(returns) for returns in returns_data.values() 
                        if len(returns) > self.min_observations)
        
        if min_length < self.min_observations:
            return {}
        
        for symbol, position in positions.items():
            if symbol in returns_data and len(returns_data[symbol]) >= min_length:
                weights[symbol] = position.market_value / total_value
        
        # Calculate historical portfolio returns
        for i in range(min_length):
            portfolio_return = sum(
                weights.get(symbol, 0) * returns_data[symbol][-min_length + i]
                for symbol in weights.keys()
            )
            portfolio_returns.append(portfolio_return)
        
        portfolio_returns = np.array(portfolio_returns)
        
        # Method 1: Historical VaR
        historical_var = np.percentile(portfolio_returns, (1 - confidence) * 100)
        
        # Method 2: Parametric VaR (assuming normal distribution)
        mean_return = np.mean(portfolio_returns)
        std_return = np.std(portfolio_returns)
        parametric_var = mean_return - stats.norm.ppf(confidence) * std_return
        
        # Method 3: Monte Carlo VaR
        monte_carlo_var = self._monte_carlo_var(portfolio_returns, confidence)
        
        # Expected Shortfall (Conditional VaR)
        var_threshold = historical_var
        tail_returns = portfolio_returns[portfolio_returns <= var_threshold]
        expected_shortfall = np.mean(tail_returns) if len(tail_returns) > 0 else historical_var
        
        return {
            'historical_var': historical_var,
            'parametric_var': parametric_var,
            'monte_carlo_var': monte_carlo_var,
            'expected_shortfall': expected_shortfall,
            'portfolio_volatility': std_return,
            'portfolio_mean_return': mean_return,
            'confidence_level': confidence
        }
    
    def _monte_carlo_var(self, returns: np.ndarray, confidence: float, 
                        num_simulations: int = 10000) -> float:
        """Calculate VaR using Monte Carlo simulation"""
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        # Generate random scenarios
        simulated_returns = np.random.normal(mean_return, std_return, num_simulations)
        
        # Calculate VaR
        var = np.percentile(simulated_returns, (1 - confidence) * 100)
        return var
    
    def calculate_component_var(self, positions: Dict[str, Position],
                              returns_data: Dict[str, np.ndarray],
                              confidence: float = 0.95) -> Dict:
        """Calculate component VaR for each position"""
        
        portfolio_var_result = self.calculate_portfolio_var(positions, returns_data, confidence)
        if not portfolio_var_result:
            return {}
        
        portfolio_var = portfolio_var_result['historical_var']
        total_value = sum(abs(pos.market_value) for pos in positions.values())
        
        component_vars = {}
        
        for symbol, position in positions.items():
            if symbol not in returns_data:
                continue
            
            # Calculate marginal VaR by removing this position
            temp_positions = {k: v for k, v in positions.items() if k != symbol}
            
            if temp_positions:
                reduced_var_result = self.calculate_portfolio_var(temp_positions, returns_data, confidence)
                reduced_var = reduced_var_result.get('historical_var', 0)
                marginal_var = portfolio_var - reduced_var
            else:
                marginal_var = portfolio_var
            
            # Component VaR = Marginal VaR * Position Weight
            position_weight = abs(position.market_value) / total_value
            component_var = marginal_var * position_weight
            
            component_vars[symbol] = {
                'component_var': component_var,
                'marginal_var': marginal_var,
                'position_weight': position_weight,
                'contribution_pct': (component_var / portfolio_var) * 100 if portfolio_var != 0 else 0
            }
        
        return component_vars
    
    def calculate_maximum_drawdown(self, portfolio_values: List[float]) -> Dict:
        """Calculate maximum drawdown and related metrics"""
        if len(portfolio_values) < 2:
            return {}
        
        values = np.array(portfolio_values)
        
        # Calculate running maximum
        peak = np.maximum.accumulate(values)
        
        # Calculate drawdown
        drawdown = (values - peak) / peak
        
        # Find maximum drawdown
        max_drawdown = np.min(drawdown)
        max_drawdown_idx = np.argmin(drawdown)
        
        # Find peak before max drawdown
        peak_idx = np.argmax(peak[:max_drawdown_idx + 1]) if max_drawdown_idx > 0 else 0
        
        # Calculate recovery information
        recovery_idx = None
        if max_drawdown_idx < len(values) - 1:
            peak_value = values[peak_idx]
            for i in range(max_drawdown_idx + 1, len(values)):
                if values[i] >= peak_value:
                    recovery_idx = i
                    break
        
        return {
            'max_drawdown': abs(max_drawdown),
            'max_drawdown_duration': max_drawdown_idx - peak_idx,
            'recovery_duration': recovery_idx - max_drawdown_idx if recovery_idx else None,
            'current_drawdown': abs(drawdown[-1]),
            'underwater_periods': len(drawdown[drawdown < -0.01])  # Periods with >1% drawdown
        }

class DynamicHedgingEngine:
    """Dynamic hedging engine for portfolio protection"""
    
    def __init__(self, hedge_instruments: List[HedgeInstrument]):
        self.hedge_instruments = {instr.symbol: instr for instr in hedge_instruments}
        self.hedge_positions: Dict[str, float] = {}
        self.rebalance_threshold = 0.05  # 5% delta threshold for rebalancing
        self.max_hedge_ratio = 0.5  # Maximum 50% of portfolio can be hedged
        
    def calculate_optimal_hedge(self, positions: Dict[str, Position],
                              target_beta: float = 0.0) -> Dict[str, float]:
        """Calculate optimal hedge positions to achieve target beta"""
        
        if not positions:
            return {}
        
        # Calculate current portfolio beta and exposure
        total_exposure = sum(pos.market_value for pos in positions.values())
        
        if total_exposure == 0:
            return {}
        
        # Simple hedge calculation (can be enhanced with optimization)
        hedge_positions = {}
        
        for hedge_symbol, hedge_instr in self.hedge_instruments.items():
            # Calculate required hedge size
            portfolio_beta = self._estimate_portfolio_beta(positions)
            beta_to_hedge = portfolio_beta - target_beta
            
            if abs(beta_to_hedge) > 0.1:  # Only hedge if significant beta exposure
                hedge_size = (beta_to_hedge / hedge_instr.hedge_ratio) * total_exposure
                
                # Apply maximum hedge ratio constraint
                max_hedge_size = total_exposure * self.max_hedge_ratio
                hedge_size = np.clip(hedge_size, -max_hedge_size, max_hedge_size)
                
                if abs(hedge_size) > total_exposure * 0.01:  # Minimum 1% threshold
                    hedge_positions[hedge_symbol] = hedge_size
        
        return hedge_positions
    
    def _estimate_portfolio_beta(self, positions: Dict[str, Position]) -> float:
        """Estimate portfolio beta (simplified calculation)"""
        # In production, this would use historical correlation with market index
        # For now, assume crypto portfolio has beta of 1.5 to crypto market
        return 1.5
    
    def calculate_hedge_effectiveness(self, portfolio_returns: np.ndarray,
                                   hedge_returns: np.ndarray) -> Dict:
        """Calculate hedge effectiveness metrics"""
        
        if len(portfolio_returns) != len(hedge_returns) or len(portfolio_returns) < 10:
            return {}
        
        # Calculate hedged portfolio returns
        hedged_returns = portfolio_returns + hedge_returns
        
        # Hedge effectiveness ratio
        portfolio_var = np.var(portfolio_returns)
        hedged_var = np.var(hedged_returns)
        
        hedge_effectiveness = (portfolio_var - hedged_var) / portfolio_var if portfolio_var > 0 else 0
        
        # Correlation between portfolio and hedge
        correlation = np.corrcoef(portfolio_returns, hedge_returns)[0, 1]
        
        # Tracking error
        tracking_error = np.std(hedged_returns - portfolio_returns)
        
        return {
            'hedge_effectiveness': hedge_effectiveness,
            'correlation': correlation,
            'tracking_error': tracking_error,
            'variance_reduction': (portfolio_var - hedged_var) / portfolio_var if portfolio_var > 0 else 0
        }

class AdvancedRiskManager:
    """Advanced risk management system"""
    
    def __init__(self, initial_capital: float):
        self.initial_capital = initial_capital
        
        # Risk limits
        self.risk_limits = {
            RiskMetric.VAR_95: RiskLimit(RiskMetric.VAR_95, 0.02, 0.015, 'reduce'),  # 2% VaR limit
            RiskMetric.MAXIMUM_DRAWDOWN: RiskLimit(RiskMetric.MAXIMUM_DRAWDOWN, 0.05, 0.03, 'close'),  # 5% max drawdown
        }
        
        # Components
        self.risk_analyzer = PortfolioRiskAnalyzer()
        self.hedging_engine = DynamicHedgingEngine([
            HedgeInstrument('SOL', -0.8, -0.85, 0.9, 5),  # SOL as hedge instrument
            HedgeInstrument('BTC', -0.6, -0.70, 0.95, 3),  # BTC as hedge instrument
        ])
        
        # Data storage
        self.returns_history: Dict[str, List[float]] = {}
        self.portfolio_values: List[float] = []
        self.risk_metrics_history: List[Dict] = []
        
        # Alerts and breaches
        self.active_alerts: List[Dict] = []
        self.risk_breaches: List[Dict] = []
        
        # Logging
        self.logger = logging.getLogger('AdvancedRiskManager')
        
    async def update_risk_metrics(self, positions: Dict[str, Position],
                                current_portfolio_value: float) -> Dict:
        """Update all risk metrics"""
        
        self.portfolio_values.append(current_portfolio_value)
        
        # Keep only recent history
        max_history = 1000
        if len(self.portfolio_values) > max_history:
            self.portfolio_values = self.portfolio_values[-max_history:]
        
        # Prepare returns data
        returns_data = {}
        for symbol in self.returns_history:
            if len(self.returns_history[symbol]) > 10:
                returns_data[symbol] = np.array(self.returns_history[symbol])
        
        # Calculate VaR metrics
        var_metrics = self.risk_analyzer.calculate_portfolio_var(positions, returns_data)
        
        # Calculate component VaR
        component_var = self.risk_analyzer.calculate_component_var(positions, returns_data)
        
        # Calculate maximum drawdown
        drawdown_metrics = self.risk_analyzer.calculate_maximum_drawdown(self.portfolio_values)
        
        # Calculate optimal hedge
        optimal_hedge = self.hedging_engine.calculate_optimal_hedge(positions)
        
        # Combine all metrics
        risk_metrics = {
            'timestamp': datetime.now(),
            'portfolio_value': current_portfolio_value,
            'var_metrics': var_metrics,
            'component_var': component_var,
            'drawdown_metrics': drawdown_metrics,
            'optimal_hedge': optimal_hedge,
            'active_positions': len(positions),
            'total_exposure': sum(abs(pos.market_value) for pos in positions.values())
        }
        
        self.risk_metrics_history.append(risk_metrics)
        
        # Check risk limits
        await self._check_risk_limits(risk_metrics)
        
        return risk_metrics
    
    async def _check_risk_limits(self, risk_metrics: Dict):
        """Check risk limits and trigger actions"""
        
        current_time = datetime.now()
        
        # Check VaR limit
        var_metrics = risk_metrics.get('var_metrics', {})
        if var_metrics:
            var_95 = abs(var_metrics.get('historical_var', 0))
            var_limit = self.risk_limits[RiskMetric.VAR_95]
            
            if var_95 > var_limit.limit_value:
                await self._handle_risk_breach(RiskMetric.VAR_95, var_95, var_limit)
            elif var_95 > var_limit.warning_threshold:
                await self._handle_risk_warning(RiskMetric.VAR_95, var_95, var_limit)
        
        # Check maximum drawdown limit
        drawdown_metrics = risk_metrics.get('drawdown_metrics', {})
        if drawdown_metrics:
            max_drawdown = drawdown_metrics.get('max_drawdown', 0)
            drawdown_limit = self.risk_limits[RiskMetric.MAXIMUM_DRAWDOWN]
            
            if max_drawdown > drawdown_limit.limit_value:
                await self._handle_risk_breach(RiskMetric.MAXIMUM_DRAWDOWN, max_drawdown, drawdown_limit)
            elif max_drawdown > drawdown_limit.warning_threshold:
                await self._handle_risk_warning(RiskMetric.MAXIMUM_DRAWDOWN, max_drawdown, drawdown_limit)
    
    async def _handle_risk_breach(self, metric: RiskMetric, value: float, limit: RiskLimit):
        """Handle risk limit breach"""
        
        breach = {
            'timestamp': datetime.now(),
            'metric': metric.value,
            'value': value,
            'limit': limit.limit_value,
            'action': limit.breach_action,
            'severity': 'CRITICAL'
        }
        
        self.risk_breaches.append(breach)
        
        self.logger.critical(
            f"RISK BREACH: {metric.value} = {value:.4f} exceeds limit {limit.limit_value:.4f}. "
            f"Action: {limit.breach_action}"
        )
        
        # Execute breach action
        if limit.breach_action == 'close':
            await self._emergency_close_positions()
        elif limit.breach_action == 'reduce':
            await self._reduce_position_sizes()
        elif limit.breach_action == 'alert':
            await self._send_risk_alert(breach)
    
    async def _handle_risk_warning(self, metric: RiskMetric, value: float, limit: RiskLimit):
        """Handle risk warning"""
        
        warning = {
            'timestamp': datetime.now(),
            'metric': metric.value,
            'value': value,
            'threshold': limit.warning_threshold,
            'severity': 'WARNING'
        }
        
        self.active_alerts.append(warning)
        
        self.logger.warning(
            f"RISK WARNING: {metric.value} = {value:.4f} exceeds warning threshold {limit.warning_threshold:.4f}"
        )
    
    async def _emergency_close_positions(self):
        """Emergency position closure"""
        self.logger.critical("EMERGENCY: Closing all positions due to risk breach")
        # Implementation would close all positions
        
    async def _reduce_position_sizes(self):
        """Reduce position sizes to manage risk"""
        self.logger.warning("Reducing position sizes due to risk breach")
        # Implementation would reduce position sizes by 50%
        
    async def _send_risk_alert(self, alert: Dict):
        """Send risk alert to monitoring systems"""
        self.logger.info(f"Risk alert sent: {alert}")
    
    def update_returns(self, symbol: str, return_value: float):
        """Update returns history for a symbol"""
        if symbol not in self.returns_history:
            self.returns_history[symbol] = []
        
        self.returns_history[symbol].append(return_value)
        
        # Keep only recent history
        max_history = 500
        if len(self.returns_history[symbol]) > max_history:
            self.returns_history[symbol] = self.returns_history[symbol][-max_history:]
    
    def get_risk_summary(self) -> Dict:
        """Get comprehensive risk summary"""
        if not self.risk_metrics_history:
            return {}
        
        latest_metrics = self.risk_metrics_history[-1]
        
        return {
            'latest_var_95': latest_metrics.get('var_metrics', {}).get('historical_var', 0),
            'latest_max_drawdown': latest_metrics.get('drawdown_metrics', {}).get('max_drawdown', 0),
            'current_drawdown': latest_metrics.get('drawdown_metrics', {}).get('current_drawdown', 0),
            'total_exposure': latest_metrics.get('total_exposure', 0),
            'active_positions': latest_metrics.get('active_positions', 0),
            'active_alerts': len(self.active_alerts),
            'risk_breaches': len(self.risk_breaches),
            'portfolio_value': latest_metrics.get('portfolio_value', 0),
            'optimal_hedge_positions': latest_metrics.get('optimal_hedge', {})
        }

# Example usage
async def test_risk_management():
    """Test the advanced risk management system"""
    
    risk_manager = AdvancedRiskManager(initial_capital=1000000)
    
    # Simulate portfolio updates
    for i in range(100):
        # Simulate returns
        risk_manager.update_returns('BONK', np.random.normal(0.001, 0.02))
        risk_manager.update_returns('WIF', np.random.normal(0.0005, 0.015))
        
        # Simulate positions
        positions = {
            'BONK': Position('BONK', 1000000, 0.000035, 35000, 0, 0, datetime.now()),
            'WIF': Position('WIF', 10000, 2.20, 22000, 0, 0, datetime.now())
        }
        
        portfolio_value = sum(pos.market_value for pos in positions.values())
        
        # Update risk metrics
        risk_metrics = await risk_manager.update_risk_metrics(positions, portfolio_value)
        
        if i % 20 == 0:
            summary = risk_manager.get_risk_summary()
            print(f"Risk Summary at step {i}: {summary}")
    
    print("Risk management test completed")

if __name__ == "__main__":
    asyncio.run(test_risk_management())
