"""
Enhanced Backtesting Framework with GMGN.ai Integration
Comprehensive backtesting system combining CSV data with GMGN.ai smart money tracking
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from data_analysis import DataAnalyzer
from enhanced_momentum_strategy import EnhancedMomentumStrategy
from enhanced_mean_reversion_strategy import EnhancedMeanReversionStrategy
from gmgn_integration import GMGNIntegration

class EnhancedBacktestingFramework:
    def __init__(self):
        """Initialize enhanced backtesting framework"""
        self.results = {}
        self.comparison_metrics = {}
        self.gmgn = GMGNIntegration()
        
    def run_enhanced_strategy_comparison(self, csv_data_path="Quant test.csv"):
        """Run enhanced strategies with both CSV and GMGN.ai data"""
        print("="*80)
        print("ENHANCED QUANTITATIVE TRADING SYSTEM WITH GMGN.AI INTEGRATION")
        print("="*80)
        print(f"Target Wallet: {self.gmgn.target_wallet}")
        
        # Step 1: Analyze CSV data
        print(f"\n{'='*60}")
        print("STEP 1: HISTORICAL DATA ANALYSIS")
        print(f"{'='*60}")
        
        analyzer = DataAnalyzer(csv_data_path)
        csv_analysis = analyzer.generate_summary_report()
        price_data = analyzer.create_price_series()
        
        if not price_data:
            print("❌ Error: No sufficient price data found")
            return None
        
        # Step 2: GMGN.ai Integration Analysis
        print(f"\n{'='*60}")
        print("STEP 2: GMGN.AI SMART MONEY ANALYSIS")
        print(f"{'='*60}")
        
        gmgn_data = self.gmgn.get_wallet_analysis()
        trending_tokens = self.gmgn.get_trending_tokens(10)
        market_sentiment = self.gmgn.get_market_sentiment()
        
        print(f"📊 GMGN.ai Wallet Analysis:")
        if gmgn_data:
            print(f"   Smart Money Score: {gmgn_data.get('smart_money_score', 0)}/10")
            print(f"   30-Day Win Rate: {gmgn_data.get('pnl_30d', {}).get('win_rate', 0)*100:.1f}%")
            print(f"   30-Day PnL: ${gmgn_data.get('pnl_30d', {}).get('realized_pnl', 0):,.0f}")
            print(f"   Total Trades: {gmgn_data.get('pnl_30d', {}).get('total_trades', 0)}")
        
        print(f"\n📈 Market Sentiment: {market_sentiment.get('sentiment', 'neutral').upper()}")
        print(f"   Confidence: {market_sentiment.get('confidence', 0)*100:.1f}%")
        
        print(f"\n🔥 Top Trending Tokens:")
        for i, token in enumerate(trending_tokens[:5], 1):
            print(f"   {i}. {token['symbol']}: ${token['price']:.6f} ({token['price_change_24h']*100:+.1f}%)")
        
        # Step 3: Enhanced Strategy Backtesting
        print(f"\n{'='*60}")
        print("STEP 3: ENHANCED STRATEGY BACKTESTING")
        print(f"{'='*60}")
        
        # Initialize enhanced strategies
        enhanced_momentum = EnhancedMomentumStrategy(
            initial_capital=100000,
            max_positions=8,
            position_size=0.12
        )
        
        enhanced_mean_reversion = EnhancedMeanReversionStrategy(
            initial_capital=100000,
            max_positions=12,
            position_size=0.08
        )
        
        # Run backtests
        print(f"\n🚀 Running Enhanced Momentum Strategy...")
        momentum_results = enhanced_momentum.run_backtest(price_data)
        enhanced_momentum.print_performance_report()
        
        print(f"\n📊 Running Enhanced Mean Reversion Strategy...")
        mean_reversion_results = enhanced_mean_reversion.run_backtest(price_data)
        enhanced_mean_reversion.print_performance_report()
        
        # Store results
        self.results = {
            'csv_analysis': csv_analysis,
            'gmgn_data': gmgn_data,
            'market_sentiment': market_sentiment,
            'trending_tokens': trending_tokens,
            'enhanced_momentum': momentum_results,
            'enhanced_mean_reversion': mean_reversion_results,
            'momentum_strategy': enhanced_momentum,
            'mean_reversion_strategy': enhanced_mean_reversion
        }
        
        # Generate enhanced comparison
        self.generate_enhanced_comparison()
        
        return self.results
    
    def generate_enhanced_comparison(self):
        """Generate enhanced comparison with GMGN.ai insights"""
        print(f"\n{'='*80}")
        print("ENHANCED STRATEGY COMPARISON WITH GMGN.AI INSIGHTS")
        print(f"{'='*80}")
        
        momentum_metrics = self.results['enhanced_momentum']
        mean_reversion_metrics = self.results['enhanced_mean_reversion']
        gmgn_data = self.results['gmgn_data']
        
        # Enhanced comparison table
        comparison_data = {
            'Metric': [
                'Total Return (%)',
                'Total Profit ($)',
                'Sharpe Ratio',
                'Win Rate (%)',
                'Max Drawdown (%)',
                'Total Trades',
                'Avg Return per Trade (%)',
                'Volatility (%)',
                'Avg Days Held',
                'GMGN Enhanced Trades',
                'GMGN Performance (%)'
            ],
            'Enhanced Momentum': [
                f"{momentum_metrics.get('total_return', 0)*100:.2f}",
                f"{momentum_metrics.get('total_profit', 0):,.2f}",
                f"{momentum_metrics.get('sharpe_ratio', 0):.3f}",
                f"{momentum_metrics.get('win_rate', 0)*100:.1f}",
                f"{momentum_metrics.get('max_drawdown', 0)*100:.2f}",
                f"{momentum_metrics.get('total_trades', 0)}",
                f"{momentum_metrics.get('avg_return_per_trade', 0)*100:.2f}",
                f"{momentum_metrics.get('volatility', 0)*100:.2f}",
                f"{momentum_metrics.get('avg_days_held', 0):.1f}",
                f"{momentum_metrics.get('gmgn_enhanced_trades', 0)}",
                f"{momentum_metrics.get('gmgn_performance', 0)*100:.2f}"
            ],
            'Enhanced Mean Reversion': [
                f"{mean_reversion_metrics.get('total_return', 0)*100:.2f}",
                f"{mean_reversion_metrics.get('total_profit', 0):,.2f}",
                f"{mean_reversion_metrics.get('sharpe_ratio', 0):.3f}",
                f"{mean_reversion_metrics.get('win_rate', 0)*100:.1f}",
                f"{mean_reversion_metrics.get('max_drawdown', 0)*100:.2f}",
                f"{mean_reversion_metrics.get('total_trades', 0)}",
                f"{mean_reversion_metrics.get('avg_return_per_trade', 0)*100:.2f}",
                f"{mean_reversion_metrics.get('volatility', 0)*100:.2f}",
                f"{mean_reversion_metrics.get('avg_days_held', 0):.1f}",
                f"{mean_reversion_metrics.get('contrarian_trades', 0)}",
                f"{mean_reversion_metrics.get('contrarian_performance', 0)*100:.2f}"
            ]
        }
        
        comparison_df = pd.DataFrame(comparison_data)
        print("\n📊 Enhanced Performance Comparison:")
        print(comparison_df.to_string(index=False))
        
        # GMGN.ai Integration Analysis
        print(f"\n{'='*60}")
        print("GMGN.AI INTEGRATION ANALYSIS")
        print(f"{'='*60}")
        
        if gmgn_data:
            smart_money_score = gmgn_data.get('smart_money_score', 0)
            wallet_win_rate = gmgn_data.get('pnl_30d', {}).get('win_rate', 0)
            
            print(f"🎯 Smart Money Alignment:")
            print(f"   Target Wallet Score: {smart_money_score}/10")
            print(f"   Target Wallet Win Rate: {wallet_win_rate*100:.1f}%")
            print(f"   Our Momentum Win Rate: {momentum_metrics.get('win_rate', 0)*100:.1f}%")
            print(f"   Our Mean Reversion Win Rate: {mean_reversion_metrics.get('win_rate', 0)*100:.1f}%")
            
            # Strategy alignment analysis
            if smart_money_score >= 7.0:
                print(f"✅ High-quality smart money signals available")
            else:
                print(f"⚠️  Moderate smart money signal quality")
            
            if wallet_win_rate > 0.6:
                print(f"✅ Target wallet shows consistent profitability")
            else:
                print(f"⚠️  Target wallet performance is mixed")
        
        # Market condition analysis
        market_sentiment = self.results['market_sentiment']
        print(f"\n🌡️  Market Conditions:")
        print(f"   Current Sentiment: {market_sentiment.get('sentiment', 'neutral').upper()}")
        print(f"   Confidence Level: {market_sentiment.get('confidence', 0)*100:.1f}%")
        
        if market_sentiment.get('sentiment') == 'bullish':
            print(f"   📈 Favorable for momentum strategies")
        elif market_sentiment.get('sentiment') == 'bearish':
            print(f"   📉 Favorable for contrarian mean reversion")
        else:
            print(f"   ⚖️  Neutral conditions - both strategies viable")
        
        # Final recommendation
        self.generate_final_recommendation()
        
        return comparison_df
    
    def generate_final_recommendation(self):
        """Generate final strategy recommendation"""
        print(f"\n{'='*80}")
        print("FINAL STRATEGY RECOMMENDATION")
        print(f"{'='*80}")
        
        momentum_metrics = self.results['enhanced_momentum']
        mean_reversion_metrics = self.results['enhanced_mean_reversion']
        gmgn_data = self.results['gmgn_data']
        market_sentiment = self.results['market_sentiment']
        
        # Determine best strategy based on multiple factors
        momentum_score = self.calculate_strategy_score(momentum_metrics, 'momentum')
        mean_reversion_score = self.calculate_strategy_score(mean_reversion_metrics, 'mean_reversion')
        
        print(f"📊 Strategy Scoring:")
        print(f"   Enhanced Momentum Score: {momentum_score:.2f}/10")
        print(f"   Enhanced Mean Reversion Score: {mean_reversion_score:.2f}/10")
        
        if momentum_score > mean_reversion_score:
            recommended_strategy = "Enhanced Momentum"
            recommended_metrics = momentum_metrics
            recommended_capital = 100000
        else:
            recommended_strategy = "Enhanced Mean Reversion"
            recommended_metrics = mean_reversion_metrics
            recommended_capital = 100000
        
        print(f"\n🏆 RECOMMENDED STRATEGY: {recommended_strategy}")
        print(f"   Expected Return: {recommended_metrics.get('total_return', 0)*100:.2f}%")
        print(f"   Risk-Adjusted Return: {recommended_metrics.get('sharpe_ratio', 0):.3f}")
        print(f"   Recommended Capital: ${recommended_capital:,.0f}")
        
        # Implementation roadmap
        print(f"\n🚀 IMPLEMENTATION ROADMAP:")
        print(f"1. Data Infrastructure:")
        print(f"   - Integrate real-time GMGN.ai API for smart money tracking")
        print(f"   - Set up Solana blockchain data feeds")
        print(f"   - Implement wallet {self.gmgn.target_wallet} monitoring")
        
        print(f"\n2. Strategy Deployment:")
        print(f"   - Deploy {recommended_strategy.lower()} with ${recommended_capital:,.0f}")
        print(f"   - Implement GMGN.ai signal integration")
        print(f"   - Set up real-time risk monitoring")
        
        print(f"\n3. Risk Management:")
        print(f"   - Monitor smart money alignment continuously")
        print(f"   - Adjust position sizing based on market sentiment")
        print(f"   - Implement dynamic stop-losses based on volatility")
        
        print(f"\n4. Performance Monitoring:")
        print(f"   - Track GMGN.ai enhanced trade performance")
        print(f"   - Monitor correlation with target wallet performance")
        print(f"   - Regular strategy optimization based on market conditions")
        
        return {
            'recommended_strategy': recommended_strategy,
            'momentum_score': momentum_score,
            'mean_reversion_score': mean_reversion_score,
            'implementation_ready': True
        }
    
    def calculate_strategy_score(self, metrics, strategy_type):
        """Calculate comprehensive strategy score"""
        if not metrics or 'error' in metrics:
            return 0
        
        # Base scoring factors
        return_score = min(max(metrics.get('total_return', 0) * 10, 0), 10)  # 0-10 scale
        sharpe_score = min(max(metrics.get('sharpe_ratio', 0) * 2, 0), 10)  # 0-10 scale
        win_rate_score = metrics.get('win_rate', 0) * 10  # 0-10 scale
        
        # Strategy-specific bonuses
        if strategy_type == 'momentum':
            gmgn_bonus = min(metrics.get('gmgn_enhanced_trades', 0) / 10, 2)  # Up to 2 bonus points
        else:
            gmgn_bonus = min(metrics.get('contrarian_trades', 0) / 5, 2)  # Up to 2 bonus points
        
        # Combined score
        total_score = (return_score * 0.3 + sharpe_score * 0.4 + win_rate_score * 0.2 + gmgn_bonus * 0.1)
        
        return min(total_score, 10)

if __name__ == "__main__":
    # Test the enhanced backtesting framework
    framework = EnhancedBacktestingFramework()
    results = framework.run_enhanced_strategy_comparison()
    
    if results:
        print(f"\n🎉 Enhanced backtesting completed successfully!")
        print(f"📁 Results include both CSV analysis and GMGN.ai integration.")
    else:
        print(f"\n❌ Enhanced backtesting encountered errors.")
